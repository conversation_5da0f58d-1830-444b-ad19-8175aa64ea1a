import { create } from 'zustand'
import { devtools, persist } from 'zustand/middleware'
import type { User, Project, Panel, Circuit, PanelDesignState } from '@/types'

interface AppState {
  // User state
  user: User | null
  setUser: (user: User | null) => void
  
  // Projects state
  projects: Project[]
  currentProject: Project | null
  setProjects: (projects: Project[]) => void
  setCurrentProject: (project: Project | null) => void
  addProject: (project: Project) => void
  updateProject: (id: string, updates: Partial<Project>) => void
  deleteProject: (id: string) => void
  
  // Panels state
  panels: Panel[]
  setPanels: (panels: Panel[]) => void
  addPanel: (panel: Panel) => void
  updatePanel: (id: string, updates: Partial<Panel>) => void
  deletePanel: (id: string) => void
  
  // Circuits state
  circuits: Circuit[]
  setCircuits: (circuits: Circuit[]) => void
  addCircuit: (circuit: Circuit) => void
  updateCircuit: (id: string, updates: Partial<Circuit>) => void
  deleteCircuit: (id: string) => void
  
  // UI state
  panelDesign: PanelDesignState
  setPanelDesign: (state: Partial<PanelDesignState>) => void
  
  // Loading states
  isLoading: boolean
  setIsLoading: (loading: boolean) => void
  
  // Error state
  error: string | null
  setError: (error: string | null) => void
}

export const useAppStore = create<AppState>()(
  devtools(
    persist(
      (set, get) => ({
        // User state
        user: null,
        setUser: (user) => set({ user }),
        
        // Projects state
        projects: [],
        currentProject: null,
        setProjects: (projects) => set({ projects }),
        setCurrentProject: (project) => set({ currentProject: project }),
        addProject: (project) => 
          set((state) => ({ projects: [...state.projects, project] })),
        updateProject: (id, updates) =>
          set((state) => ({
            projects: state.projects.map((p) =>
              p.id === id ? { ...p, ...updates } : p
            ),
            currentProject: state.currentProject?.id === id 
              ? { ...state.currentProject, ...updates }
              : state.currentProject
          })),
        deleteProject: (id) =>
          set((state) => ({
            projects: state.projects.filter((p) => p.id !== id),
            currentProject: state.currentProject?.id === id ? null : state.currentProject
          })),
        
        // Panels state
        panels: [],
        setPanels: (panels) => set({ panels }),
        addPanel: (panel) =>
          set((state) => ({ panels: [...state.panels, panel] })),
        updatePanel: (id, updates) =>
          set((state) => ({
            panels: state.panels.map((p) =>
              p.id === id ? { ...p, ...updates } : p
            )
          })),
        deletePanel: (id) =>
          set((state) => ({
            panels: state.panels.filter((p) => p.id !== id)
          })),
        
        // Circuits state
        circuits: [],
        setCircuits: (circuits) => set({ circuits }),
        addCircuit: (circuit) =>
          set((state) => ({ circuits: [...state.circuits, circuit] })),
        updateCircuit: (id, updates) =>
          set((state) => ({
            circuits: state.circuits.map((c) =>
              c.id === id ? { ...c, ...updates } : c
            )
          })),
        deleteCircuit: (id) =>
          set((state) => ({
            circuits: state.circuits.filter((c) => c.id !== id)
          })),
        
        // UI state
        panelDesign: {
          selectedPanel: null,
          selectedCircuit: null,
          viewMode: '2d',
          showCalculations: false,
          showCompliance: false,
        },
        setPanelDesign: (state) =>
          set((current) => ({
            panelDesign: { ...current.panelDesign, ...state }
          })),
        
        // Loading states
        isLoading: false,
        setIsLoading: (loading) => set({ isLoading: loading }),
        
        // Error state
        error: null,
        setError: (error) => set({ error }),
      }),
      {
        name: 'electrical-panel-app-storage',
        partialize: (state) => ({
          user: state.user,
          currentProject: state.currentProject,
          panelDesign: state.panelDesign,
        }),
      }
    ),
    {
      name: 'electrical-panel-app',
    }
  )
)

// Selectors for better performance
export const useUser = () => useAppStore((state) => state.user)
export const useProjects = () => useAppStore((state) => state.projects)
export const useCurrentProject = () => useAppStore((state) => state.currentProject)
export const usePanels = () => useAppStore((state) => state.panels)
export const useCircuits = () => useAppStore((state) => state.circuits)
export const usePanelDesign = () => useAppStore((state) => state.panelDesign)
export const useIsLoading = () => useAppStore((state) => state.isLoading)
export const useError = () => useAppStore((state) => state.error)
