export declare enum ContextualKeyword {
    NONE = 0,
    _abstract = 1,
    _accessor = 2,
    _as = 3,
    _assert = 4,
    _asserts = 5,
    _async = 6,
    _await = 7,
    _checks = 8,
    _constructor = 9,
    _declare = 10,
    _enum = 11,
    _exports = 12,
    _from = 13,
    _get = 14,
    _global = 15,
    _implements = 16,
    _infer = 17,
    _interface = 18,
    _is = 19,
    _keyof = 20,
    _mixins = 21,
    _module = 22,
    _namespace = 23,
    _of = 24,
    _opaque = 25,
    _out = 26,
    _override = 27,
    _private = 28,
    _protected = 29,
    _proto = 30,
    _public = 31,
    _readonly = 32,
    _require = 33,
    _satisfies = 34,
    _set = 35,
    _static = 36,
    _symbol = 37,
    _type = 38,
    _unique = 39,
    _using = 40
}
