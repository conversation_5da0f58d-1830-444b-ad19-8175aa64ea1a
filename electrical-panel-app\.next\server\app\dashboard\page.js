(()=>{var e={};e.id=105,e.ids=[105],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},2704:()=>{},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3501:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,6444,23)),Promise.resolve().then(t.t.bind(t,6042,23)),Promise.resolve().then(t.t.bind(t,8170,23)),Promise.resolve().then(t.t.bind(t,9477,23)),Promise.resolve().then(t.t.bind(t,9345,23)),Promise.resolve().then(t.t.bind(t,2089,23)),Promise.resolve().then(t.t.bind(t,6577,23)),Promise.resolve().then(t.t.bind(t,1307,23))},3873:e=>{"use strict";e.exports=require("path")},4118:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder (16)\\\\electrical-panel-app\\\\app\\\\dashboard\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\New folder (16)\\electrical-panel-app\\app\\dashboard\\page.tsx","default")},4934:(e,s,t)=>{"use strict";t.d(s,{$:()=>d});var r=t(687),a=t(3210),n=t(4224),i=t(6241);let l=(0,n.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=a.forwardRef(({className:e,variant:s,size:t,...a},n)=>(0,r.jsx)("button",{className:(0,i.cn)(l({variant:s,size:t,className:e})),ref:n,...a}));d.displayName="Button"},4941:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,6346,23)),Promise.resolve().then(t.t.bind(t,7924,23)),Promise.resolve().then(t.t.bind(t,5656,23)),Promise.resolve().then(t.t.bind(t,99,23)),Promise.resolve().then(t.t.bind(t,8243,23)),Promise.resolve().then(t.t.bind(t,8827,23)),Promise.resolve().then(t.t.bind(t,2763,23)),Promise.resolve().then(t.t.bind(t,7173,23))},5192:(e,s,t)=>{"use strict";t.d(s,{BT:()=>c,Wu:()=>o,ZB:()=>d,Zp:()=>i,aR:()=>l});var r=t(687),a=t(3210),n=t(6241);let i=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("div",{ref:t,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...s}));i.displayName="Card";let l=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("div",{ref:t,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",e),...s}));l.displayName="CardHeader";let d=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("h3",{ref:t,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",e),...s}));d.displayName="CardTitle";let c=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("p",{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",e),...s}));c.displayName="CardDescription";let o=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("div",{ref:t,className:(0,n.cn)("p-6 pt-0",e),...s}));o.displayName="CardContent",a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("div",{ref:t,className:(0,n.cn)("flex items-center p-6 pt-0",e),...s})).displayName="CardFooter"},5611:(e,s,t)=>{Promise.resolve().then(t.bind(t,4118))},5902:()=>{},6055:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});var r=t(1658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},6241:(e,s,t)=>{"use strict";t.d(s,{WM:()=>i,cn:()=>n,r4:()=>d,un:()=>l});var r=t(9384),a=t(2348);function n(...e){return(0,a.QP)((0,r.$)(e))}let i={VOLTAGE_120:120,VOLTAGE_240:240,VOLTAGE_208:208,VOLTAGE_480:480,WIRE_RESISTANCE:{14:3.07,12:1.93,10:1.21,8:.764,6:.491,4:.308,3:.245,2:.194,1:.154,"1/0":.122,"2/0":.0967,"3/0":.0766,"4/0":.0608},WIRE_AMPACITY:{14:20,12:25,10:35,8:50,6:65,4:85,3:100,2:115,1:130,"1/0":150,"2/0":175,"3/0":200,"4/0":230}};function l(e,s,t,r=240){let a=i.WIRE_RESISTANCE[t];return a?Math.round(2*e*a*s/1e3/r*1e4)/100:0}function d(e,s,t=3){for(let r of Object.keys(i.WIRE_AMPACITY))if(i.WIRE_AMPACITY[r]>=1.25*e&&l(e,s,r)<=t)return r;return"4/0"}},7529:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>o,routeModule:()=>x,tree:()=>c});var r=t(5239),a=t(8088),n=t(8170),i=t.n(n),l=t(893),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);t.d(s,d);let c={children:["",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,4118)),"C:\\Users\\<USER>\\Desktop\\New folder (16)\\electrical-panel-app\\app\\dashboard\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,6055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,8014)),"C:\\Users\\<USER>\\Desktop\\New folder (16)\\electrical-panel-app\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,6055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\Users\\<USER>\\Desktop\\New folder (16)\\electrical-panel-app\\app\\dashboard\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},8014:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>c,metadata:()=>d});var r=t(7413),a=t(260),n=t.n(a),i=t(3298),l=t.n(i);t(2704);let d={title:"ElectriPanel Pro - Electrical Panel Design & Management",description:"Professional electrical panel board design, planning, and documentation for electricians, engineers, and contractors.",keywords:["electrical panel","panel board","electrical design","NEC compliance","electrical calculations"],authors:[{name:"ElectriPanel Pro"}],viewport:"width=device-width, initial-scale=1",themeColor:"#3b82f6",manifest:"/manifest.json"};function c({children:e}){return(0,r.jsx)("html",{lang:"en",suppressHydrationWarning:!0,children:(0,r.jsx)("body",{className:`${n().variable} ${l().variable} antialiased min-h-screen bg-background font-sans`,children:(0,r.jsx)("div",{className:"relative flex min-h-screen flex-col",children:(0,r.jsx)("main",{className:"flex-1",children:e})})})})}},8755:(e,s,t)=>{Promise.resolve().then(t.bind(t,8941))},8941:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>y});var r=t(687),a=t(3210),n=t(5814),i=t.n(n),l=t(4934),d=t(5192),c=t(5583),o=t(4027),m=t(8869),x=t(2688);let p=(0,x.A)("folder-open",[["path",{d:"m6 14 1.5-2.9A2 2 0 0 1 9.24 10H20a2 2 0 0 1 1.94 2.5l-1.54 6a2 2 0 0 1-1.95 1.5H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h3.9a2 2 0 0 1 1.69.9l.81 1.2a2 2 0 0 0 1.67.9H18a2 2 0 0 1 2 2v2",key:"usdka0"}]]),h=(0,x.A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]);var u=t(9402);let f=(0,x.A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]),j=(0,x.A)("ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]]),v=(0,x.A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]]),b=(0,x.A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]),g=[{id:"1",name:"Downtown Office Building",description:"Main electrical panel for 15-story office complex",location:"123 Business Ave, Downtown",status:"in_progress",created_at:"2024-01-15",panels:3},{id:"2",name:"Residential Complex Phase 2",description:"Distribution panels for apartment units 201-250",location:"456 Residential St, Suburbs",status:"review",created_at:"2024-01-10",panels:8},{id:"3",name:"Manufacturing Plant Upgrade",description:"Industrial panel replacement and expansion",location:"789 Industrial Blvd, Industrial Park",status:"draft",created_at:"2024-01-08",panels:2}],N={draft:"bg-gray-100 text-gray-800",in_progress:"bg-blue-100 text-blue-800",review:"bg-yellow-100 text-yellow-800",approved:"bg-green-100 text-green-800",completed:"bg-purple-100 text-purple-800"};function y(){let[e]=(0,a.useState)(g);return(0,r.jsxs)("div",{className:"min-h-screen bg-background",children:[(0,r.jsx)("header",{className:"border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",children:(0,r.jsxs)("div",{className:"container flex h-16 items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(c.A,{className:"h-6 w-6 text-primary"}),(0,r.jsx)("span",{className:"text-xl font-bold",children:"ElectriPanel Pro"})]}),(0,r.jsxs)("nav",{className:"hidden md:flex items-center space-x-6",children:[(0,r.jsx)(i(),{href:"/dashboard",className:"text-sm font-medium text-primary",children:"Dashboard"}),(0,r.jsx)(i(),{href:"/projects",className:"text-sm font-medium hover:text-primary",children:"Projects"}),(0,r.jsx)(i(),{href:"/design",className:"text-sm font-medium hover:text-primary",children:"Design"}),(0,r.jsx)(i(),{href:"/calculations",className:"text-sm font-medium hover:text-primary",children:"Calculations"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)(l.$,{variant:"ghost",size:"icon",children:(0,r.jsx)(o.A,{className:"h-4 w-4"})}),(0,r.jsx)(l.$,{variant:"ghost",size:"icon",children:(0,r.jsx)(m.A,{className:"h-4 w-4"})})]})]})}),(0,r.jsxs)("div",{className:"container py-8",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold mb-2",children:"Welcome back!"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Manage your electrical panel projects and designs from your dashboard."})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[(0,r.jsxs)(d.Zp,{children:[(0,r.jsxs)(d.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(d.ZB,{className:"text-sm font-medium",children:"Total Projects"}),(0,r.jsx)(p,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsxs)(d.Wu,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:e.length}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"+2 from last month"})]})]}),(0,r.jsxs)(d.Zp,{children:[(0,r.jsxs)(d.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(d.ZB,{className:"text-sm font-medium",children:"Active Panels"}),(0,r.jsx)(c.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsxs)(d.Wu,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:e.reduce((e,s)=>e+s.panels,0)}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Across all projects"})]})]}),(0,r.jsxs)(d.Zp,{children:[(0,r.jsxs)(d.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(d.ZB,{className:"text-sm font-medium",children:"In Review"}),(0,r.jsx)(h,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsxs)(d.Wu,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:e.filter(e=>"review"===e.status).length}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Pending approval"})]})]}),(0,r.jsxs)(d.Zp,{children:[(0,r.jsxs)(d.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(d.ZB,{className:"text-sm font-medium",children:"Calculations"}),(0,r.jsx)(u.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsxs)(d.Wu,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:"24"}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"This month"})]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[(0,r.jsx)(d.Zp,{className:"cursor-pointer hover:shadow-md transition-shadow",children:(0,r.jsxs)(d.aR,{children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(f,{className:"h-5 w-5 text-primary"}),(0,r.jsx)(d.ZB,{className:"text-lg",children:"New Project"})]}),(0,r.jsx)(d.BT,{children:"Start a new electrical panel design project"})]})}),(0,r.jsx)(d.Zp,{className:"cursor-pointer hover:shadow-md transition-shadow",children:(0,r.jsxs)(d.aR,{children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(u.A,{className:"h-5 w-5 text-primary"}),(0,r.jsx)(d.ZB,{className:"text-lg",children:"Load Calculator"})]}),(0,r.jsx)(d.BT,{children:"Calculate electrical loads and requirements"})]})}),(0,r.jsx)(d.Zp,{className:"cursor-pointer hover:shadow-md transition-shadow",children:(0,r.jsxs)(d.aR,{children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(h,{className:"h-5 w-5 text-primary"}),(0,r.jsx)(d.ZB,{className:"text-lg",children:"Generate Schedule"})]}),(0,r.jsx)(d.BT,{children:"Create panel schedules and documentation"})]})})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold",children:"Recent Projects"}),(0,r.jsx)(l.$,{asChild:!0,children:(0,r.jsx)(i(),{href:"/projects",children:"View All"})})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:e.map(e=>(0,r.jsxs)(d.Zp,{className:"cursor-pointer hover:shadow-md transition-shadow",children:[(0,r.jsxs)(d.aR,{children:[(0,r.jsxs)("div",{className:"flex items-start justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(d.ZB,{className:"text-lg mb-1",children:e.name}),(0,r.jsx)(d.BT,{className:"mb-2",children:e.description})]}),(0,r.jsx)(l.$,{variant:"ghost",size:"icon",children:(0,r.jsx)(j,{className:"h-4 w-4"})})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-muted-foreground",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(v,{className:"h-3 w-3"}),(0,r.jsx)("span",{children:e.location})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(b,{className:"h-3 w-3"}),(0,r.jsx)("span",{children:new Date(e.created_at).toLocaleDateString()})]})]})]}),(0,r.jsx)(d.Wu,{children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${N[e.status]}`,children:e.status.replace("_"," ")}),(0,r.jsxs)("span",{className:"text-sm text-muted-foreground",children:[e.panels," panel",1!==e.panels?"s":""]})]}),(0,r.jsx)(l.$,{variant:"outline",size:"sm",children:"Open"})]})})]},e.id))})]})]})]})}},8950:()=>{},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[447,423,567,830],()=>t(7529));module.exports=r})();