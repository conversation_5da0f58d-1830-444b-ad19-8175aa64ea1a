(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[105],{3999:(e,s,t)=>{"use strict";t.d(s,{WM:()=>n,cn:()=>l,r4:()=>i,un:()=>c});var a=t(2596),r=t(9688);function l(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,r.QP)((0,a.$)(s))}let n={VOLTAGE_120:120,VOLTAGE_240:240,VOLTAGE_208:208,VOLTAGE_480:480,WIRE_RESISTANCE:{14:3.07,12:1.93,10:1.21,8:.764,6:.491,4:.308,3:.245,2:.194,1:.154,"1/0":.122,"2/0":.0967,"3/0":.0766,"4/0":.0608},WIRE_AMPACITY:{14:20,12:25,10:35,8:50,6:65,4:85,3:100,2:115,1:130,"1/0":150,"2/0":175,"3/0":200,"4/0":230}};function c(e,s,t){let a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:240,r=n.WIRE_RESISTANCE[t];return r?Math.round(2*e*r*s/1e3/a*1e4)/100:0}function i(e,s){let t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:3;for(let a of Object.keys(n.WIRE_AMPACITY))if(n.WIRE_AMPACITY[a]>=1.25*e&&c(e,s,a)<=t)return a;return"4/0"}},4895:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>y});var a=t(5155),r=t(2115),l=t(6874),n=t.n(l),c=t(7168),i=t(8482),d=t(1539),o=t(381),x=t(1007),m=t(9946);let h=(0,m.A)("folder-open",[["path",{d:"m6 14 1.5-2.9A2 2 0 0 1 9.24 10H20a2 2 0 0 1 1.94 2.5l-1.54 6a2 2 0 0 1-1.95 1.5H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h3.9a2 2 0 0 1 1.69.9l.81 1.2a2 2 0 0 0 1.67.9H18a2 2 0 0 1 2 2v2",key:"usdka0"}]]),u=(0,m.A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]);var p=t(6740);let f=(0,m.A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]),j=(0,m.A)("ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]]),g=(0,m.A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]]),N=(0,m.A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]),v=[{id:"1",name:"Downtown Office Building",description:"Main electrical panel for 15-story office complex",location:"123 Business Ave, Downtown",status:"in_progress",created_at:"2024-01-15",panels:3},{id:"2",name:"Residential Complex Phase 2",description:"Distribution panels for apartment units 201-250",location:"456 Residential St, Suburbs",status:"review",created_at:"2024-01-10",panels:8},{id:"3",name:"Manufacturing Plant Upgrade",description:"Industrial panel replacement and expansion",location:"789 Industrial Blvd, Industrial Park",status:"draft",created_at:"2024-01-08",panels:2}],b={draft:"bg-gray-100 text-gray-800",in_progress:"bg-blue-100 text-blue-800",review:"bg-yellow-100 text-yellow-800",approved:"bg-green-100 text-green-800",completed:"bg-purple-100 text-purple-800"};function y(){let[e]=(0,r.useState)(v);return(0,a.jsxs)("div",{className:"min-h-screen bg-background",children:[(0,a.jsx)("header",{className:"border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",children:(0,a.jsxs)("div",{className:"container flex h-16 items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(d.A,{className:"h-6 w-6 text-primary"}),(0,a.jsx)("span",{className:"text-xl font-bold",children:"ElectriPanel Pro"})]}),(0,a.jsxs)("nav",{className:"hidden md:flex items-center space-x-6",children:[(0,a.jsx)(n(),{href:"/dashboard",className:"text-sm font-medium text-primary",children:"Dashboard"}),(0,a.jsx)(n(),{href:"/projects",className:"text-sm font-medium hover:text-primary",children:"Projects"}),(0,a.jsx)(n(),{href:"/design",className:"text-sm font-medium hover:text-primary",children:"Design"}),(0,a.jsx)(n(),{href:"/calculations",className:"text-sm font-medium hover:text-primary",children:"Calculations"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)(c.$,{variant:"ghost",size:"icon",children:(0,a.jsx)(o.A,{className:"h-4 w-4"})}),(0,a.jsx)(c.$,{variant:"ghost",size:"icon",children:(0,a.jsx)(x.A,{className:"h-4 w-4"})})]})]})}),(0,a.jsxs)("div",{className:"container py-8",children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold mb-2",children:"Welcome back!"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Manage your electrical panel projects and designs from your dashboard."})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(i.ZB,{className:"text-sm font-medium",children:"Total Projects"}),(0,a.jsx)(h,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsxs)(i.Wu,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:e.length}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"+2 from last month"})]})]}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(i.ZB,{className:"text-sm font-medium",children:"Active Panels"}),(0,a.jsx)(d.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsxs)(i.Wu,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:e.reduce((e,s)=>e+s.panels,0)}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Across all projects"})]})]}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(i.ZB,{className:"text-sm font-medium",children:"In Review"}),(0,a.jsx)(u,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsxs)(i.Wu,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:e.filter(e=>"review"===e.status).length}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Pending approval"})]})]}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(i.ZB,{className:"text-sm font-medium",children:"Calculations"}),(0,a.jsx)(p.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsxs)(i.Wu,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:"24"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"This month"})]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[(0,a.jsx)(i.Zp,{className:"cursor-pointer hover:shadow-md transition-shadow",children:(0,a.jsxs)(i.aR,{children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(f,{className:"h-5 w-5 text-primary"}),(0,a.jsx)(i.ZB,{className:"text-lg",children:"New Project"})]}),(0,a.jsx)(i.BT,{children:"Start a new electrical panel design project"})]})}),(0,a.jsx)(i.Zp,{className:"cursor-pointer hover:shadow-md transition-shadow",children:(0,a.jsxs)(i.aR,{children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(p.A,{className:"h-5 w-5 text-primary"}),(0,a.jsx)(i.ZB,{className:"text-lg",children:"Load Calculator"})]}),(0,a.jsx)(i.BT,{children:"Calculate electrical loads and requirements"})]})}),(0,a.jsx)(i.Zp,{className:"cursor-pointer hover:shadow-md transition-shadow",children:(0,a.jsxs)(i.aR,{children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(u,{className:"h-5 w-5 text-primary"}),(0,a.jsx)(i.ZB,{className:"text-lg",children:"Generate Schedule"})]}),(0,a.jsx)(i.BT,{children:"Create panel schedules and documentation"})]})})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold",children:"Recent Projects"}),(0,a.jsx)(c.$,{asChild:!0,children:(0,a.jsx)(n(),{href:"/projects",children:"View All"})})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:e.map(e=>(0,a.jsxs)(i.Zp,{className:"cursor-pointer hover:shadow-md transition-shadow",children:[(0,a.jsxs)(i.aR,{children:[(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(i.ZB,{className:"text-lg mb-1",children:e.name}),(0,a.jsx)(i.BT,{className:"mb-2",children:e.description})]}),(0,a.jsx)(c.$,{variant:"ghost",size:"icon",children:(0,a.jsx)(j,{className:"h-4 w-4"})})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-muted-foreground",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(g,{className:"h-3 w-3"}),(0,a.jsx)("span",{children:e.location})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(N,{className:"h-3 w-3"}),(0,a.jsx)("span",{children:new Date(e.created_at).toLocaleDateString()})]})]})]}),(0,a.jsx)(i.Wu,{children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat(b[e.status]),children:e.status.replace("_"," ")}),(0,a.jsxs)("span",{className:"text-sm text-muted-foreground",children:[e.panels," panel",1!==e.panels?"s":""]})]}),(0,a.jsx)(c.$,{variant:"outline",size:"sm",children:"Open"})]})})]},e.id))})]})]})]})}},5533:(e,s,t)=>{Promise.resolve().then(t.bind(t,4895))},7168:(e,s,t)=>{"use strict";t.d(s,{$:()=>i});var a=t(5155),r=t(2115),l=t(2085),n=t(3999);let c=(0,l.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),i=r.forwardRef((e,s)=>{let{className:t,variant:r,size:l,...i}=e;return(0,a.jsx)("button",{className:(0,n.cn)(c({variant:r,size:l,className:t})),ref:s,...i})});i.displayName="Button"},8482:(e,s,t)=>{"use strict";t.d(s,{BT:()=>d,Wu:()=>o,ZB:()=>i,Zp:()=>n,aR:()=>c});var a=t(5155),r=t(2115),l=t(3999);let n=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,l.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),...r})});n.displayName="Card";let c=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",t),...r})});c.displayName="CardHeader";let i=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("h3",{ref:s,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",t),...r})});i.displayName="CardTitle";let d=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("p",{ref:s,className:(0,l.cn)("text-sm text-muted-foreground",t),...r})});d.displayName="CardDescription";let o=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,l.cn)("p-6 pt-0",t),...r})});o.displayName="CardContent",r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,l.cn)("flex items-center p-6 pt-0",t),...r})}).displayName="CardFooter"}},e=>{var s=s=>e(e.s=s);e.O(0,[874,830,441,684,358],()=>s(5533)),_N_E=e.O()}]);