import Link from "next/link";
import { But<PERSON> } from "../components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "../components/ui/card";
import {
  Zap,
  Calculator,
  Shield,
  FileText,
  Users,
  CheckCircle,
  ArrowRight,
  Lightbulb,
  Settings,
  BarChart3
} from "lucide-react";

export default function Home() {
  return (
    <div className="flex flex-col min-h-screen">
      {/* Header */}
      <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container flex h-16 items-center justify-between">
          <div className="flex items-center space-x-2">
            <Zap className="h-6 w-6 text-primary" />
            <span className="text-xl font-bold">ElectriPanel Pro</span>
          </div>
          <nav className="hidden md:flex items-center space-x-6">
            <Link href="#features" className="text-sm font-medium hover:text-primary">
              Features
            </Link>
            <Link href="#pricing" className="text-sm font-medium hover:text-primary">
              Pricing
            </Link>
            <Link href="#about" className="text-sm font-medium hover:text-primary">
              About
            </Link>
          </nav>
          <div className="flex items-center space-x-4">
            <Button variant="ghost" asChild>
              <Link href="/login">Sign In</Link>
            </Button>
            <Button asChild>
              <Link href="/dashboard">Get Started</Link>
            </Button>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="flex-1 flex items-center justify-center py-20 px-4">
        <div className="container max-w-6xl text-center">
          <div className="mx-auto max-w-3xl">
            <h1 className="text-4xl font-bold tracking-tight sm:text-6xl mb-6">
              Professional Electrical Panel Design & Management
            </h1>
            <p className="text-lg text-muted-foreground mb-8 max-w-2xl mx-auto">
              Streamline your electrical panel board design, planning, and documentation.
              Built for electricians, engineers, and contractors who demand precision and compliance.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" asChild>
                <Link href="/dashboard">
                  Start Designing <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button size="lg" variant="outline" asChild>
                <Link href="#features">Learn More</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-20 bg-muted/50">
        <div className="container max-w-6xl">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold mb-4">Powerful Features for Electrical Professionals</h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Everything you need to design, calculate, and document electrical panel boards with confidence.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <Card>
              <CardHeader>
                <Lightbulb className="h-8 w-8 text-primary mb-2" />
                <CardTitle>Interactive Design</CardTitle>
                <CardDescription>
                  Drag-and-drop panel layout builder with 2D/3D visualization
                </CardDescription>
              </CardHeader>
            </Card>

            <Card>
              <CardHeader>
                <Calculator className="h-8 w-8 text-primary mb-2" />
                <CardTitle>Smart Calculations</CardTitle>
                <CardDescription>
                  Automated load calculations, voltage drop analysis, and wire sizing per NEC Article 220
                </CardDescription>
              </CardHeader>
            </Card>

            <Card>
              <CardHeader>
                <Shield className="h-8 w-8 text-primary mb-2" />
                <CardTitle>Code Compliance</CardTitle>
                <CardDescription>
                  Real-time compliance checking with built-in NEC, IEC, and local code references
                </CardDescription>
              </CardHeader>
            </Card>

            <Card>
              <CardHeader>
                <FileText className="h-8 w-8 text-primary mb-2" />
                <CardTitle>Documentation</CardTitle>
                <CardDescription>
                  Generate permit-ready panel schedules, material lists, and specifications
                </CardDescription>
              </CardHeader>
            </Card>

            <Card>
              <CardHeader>
                <Settings className="h-8 w-8 text-primary mb-2" />
                <CardTitle>Component Database</CardTitle>
                <CardDescription>
                  Searchable database of manufacturers' components with compatibility checking
                </CardDescription>
              </CardHeader>
            </Card>

            <Card>
              <CardHeader>
                <BarChart3 className="h-8 w-8 text-primary mb-2" />
                <CardTitle>Project Management</CardTitle>
                <CardDescription>
                  Track projects, collaborate with teams, and maintain version history
                </CardDescription>
              </CardHeader>
            </Card>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20">
        <div className="container max-w-4xl text-center">
          <h2 className="text-3xl font-bold mb-4">Ready to Transform Your Electrical Design Process?</h2>
          <p className="text-lg text-muted-foreground mb-8">
            Join thousands of electrical professionals who trust ElectriPanel Pro for their panel design needs.
          </p>
          <Button size="lg" asChild>
            <Link href="/dashboard">
              Get Started Today <ArrowRight className="ml-2 h-4 w-4" />
            </Link>
          </Button>
        </div>
      </section>

      {/* Footer */}
      <footer className="border-t bg-muted/50">
        <div className="container py-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="flex items-center space-x-2 mb-4 md:mb-0">
              <Zap className="h-5 w-5 text-primary" />
              <span className="font-semibold">ElectriPanel Pro</span>
            </div>
            <p className="text-sm text-muted-foreground">
              © 2024 ElectriPanel Pro. All rights reserved.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}
