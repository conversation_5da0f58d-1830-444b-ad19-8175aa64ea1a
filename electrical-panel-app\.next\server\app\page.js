(()=>{var e={};e.id=974,e.ids=[974],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1513:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,4536,23))},2704:()=>{},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3501:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,6444,23)),Promise.resolve().then(t.t.bind(t,6042,23)),Promise.resolve().then(t.t.bind(t,8170,23)),Promise.resolve().then(t.t.bind(t,9477,23)),Promise.resolve().then(t.t.bind(t,9345,23)),Promise.resolve().then(t.t.bind(t,2089,23)),Promise.resolve().then(t.t.bind(t,6577,23)),Promise.resolve().then(t.t.bind(t,1307,23))},3873:e=>{"use strict";e.exports=require("path")},4536:(e,r,t)=>{let{createProxy:o}=t(9844);e.exports=o("C:\\Users\\<USER>\\Desktop\\New folder (16)\\electrical-panel-app\\node_modules\\next\\dist\\client\\app-dir\\link.js")},4665:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,5814,23))},4941:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,6346,23)),Promise.resolve().then(t.t.bind(t,7924,23)),Promise.resolve().then(t.t.bind(t,5656,23)),Promise.resolve().then(t.t.bind(t,99,23)),Promise.resolve().then(t.t.bind(t,8243,23)),Promise.resolve().then(t.t.bind(t,8827,23)),Promise.resolve().then(t.t.bind(t,2763,23)),Promise.resolve().then(t.t.bind(t,7173,23))},5902:()=>{},6055:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var o=t(1658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,o.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},6507:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>p,tree:()=>d});var o=t(5239),a=t(8088),s=t(8170),n=t.n(s),l=t(893),i={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>l[e]);t.d(r,i);let d={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,8797)),"C:\\Users\\<USER>\\Desktop\\New folder (16)\\electrical-panel-app\\app\\page.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,6055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,8014)),"C:\\Users\\<USER>\\Desktop\\New folder (16)\\electrical-panel-app\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,6055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\New folder (16)\\electrical-panel-app\\app\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},p=new o.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},8014:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>d,metadata:()=>i});var o=t(7413),a=t(260),s=t.n(a),n=t(3298),l=t.n(n);t(2704);let i={title:"ElectriPanel Pro - Electrical Panel Design & Management",description:"Professional electrical panel board design, planning, and documentation for electricians, engineers, and contractors.",keywords:["electrical panel","panel board","electrical design","NEC compliance","electrical calculations"],authors:[{name:"ElectriPanel Pro"}],viewport:"width=device-width, initial-scale=1",themeColor:"#3b82f6",manifest:"/manifest.json"};function d({children:e}){return(0,o.jsx)("html",{lang:"en",suppressHydrationWarning:!0,children:(0,o.jsx)("body",{className:`${s().variable} ${l().variable} antialiased min-h-screen bg-background font-sans`,children:(0,o.jsx)("div",{className:"relative flex min-h-screen flex-col",children:(0,o.jsx)("main",{className:"flex-1",children:e})})})})}},8797:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>eO});var o=t(7413),a=t(4536),s=t.n(a),n=t(1120);function l(){for(var e,r,t=0,o="",a=arguments.length;t<a;t++)(e=arguments[t])&&(r=function e(r){var t,o,a="";if("string"==typeof r||"number"==typeof r)a+=r;else if("object"==typeof r)if(Array.isArray(r)){var s=r.length;for(t=0;t<s;t++)r[t]&&(o=e(r[t]))&&(a&&(a+=" "),a+=o)}else for(o in r)r[o]&&(a&&(a+=" "),a+=o);return a}(e))&&(o&&(o+=" "),o+=r);return o}let i=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,d=e=>{let r=u(e),{conflictingClassGroups:t,conflictingClassGroupModifiers:o}=e;return{getClassGroupId:e=>{let t=e.split("-");return""===t[0]&&1!==t.length&&t.shift(),c(t,r)||p(e)},getConflictingClassGroupIds:(e,r)=>{let a=t[e]||[];return r&&o[e]?[...a,...o[e]]:a}}},c=(e,r)=>{if(0===e.length)return r.classGroupId;let t=e[0],o=r.nextPart.get(t),a=o?c(e.slice(1),o):void 0;if(a)return a;if(0===r.validators.length)return;let s=e.join("-");return r.validators.find(({validator:e})=>e(s))?.classGroupId},m=/^\[(.+)\]$/,p=e=>{if(m.test(e)){let r=m.exec(e)[1],t=r?.substring(0,r.indexOf(":"));if(t)return"arbitrary.."+t}},u=e=>{let{theme:r,classGroups:t}=e,o={nextPart:new Map,validators:[]};for(let e in t)h(t[e],o,e,r);return o},h=(e,r,t,o)=>{e.forEach(e=>{if("string"==typeof e){(""===e?r:f(r,e)).classGroupId=t;return}if("function"==typeof e)return b(e)?void h(e(o),r,t,o):void r.validators.push({validator:e,classGroupId:t});Object.entries(e).forEach(([e,a])=>{h(a,f(r,e),t,o)})})},f=(e,r)=>{let t=e;return r.split("-").forEach(e=>{t.nextPart.has(e)||t.nextPart.set(e,{nextPart:new Map,validators:[]}),t=t.nextPart.get(e)}),t},b=e=>e.isThemeGetter,x=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let r=0,t=new Map,o=new Map,a=(a,s)=>{t.set(a,s),++r>e&&(r=0,o=t,t=new Map)};return{get(e){let r=t.get(e);return void 0!==r?r:void 0!==(r=o.get(e))?(a(e,r),r):void 0},set(e,r){t.has(e)?t.set(e,r):a(e,r)}}},g=e=>{let{prefix:r,experimentalParseClassName:t}=e,o=e=>{let r,t=[],o=0,a=0,s=0;for(let n=0;n<e.length;n++){let l=e[n];if(0===o&&0===a){if(":"===l){t.push(e.slice(s,n)),s=n+1;continue}if("/"===l){r=n;continue}}"["===l?o++:"]"===l?o--:"("===l?a++:")"===l&&a--}let n=0===t.length?e:e.substring(s),l=v(n);return{modifiers:t,hasImportantModifier:l!==n,baseClassName:l,maybePostfixModifierPosition:r&&r>s?r-s:void 0}};if(r){let e=r+":",t=o;o=r=>r.startsWith(e)?t(r.substring(e.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:r,maybePostfixModifierPosition:void 0}}if(t){let e=o;o=r=>t({className:r,parseClassName:e})}return o},v=e=>e.endsWith("!")?e.substring(0,e.length-1):e.startsWith("!")?e.substring(1):e,y=e=>{let r=Object.fromEntries(e.orderSensitiveModifiers.map(e=>[e,!0]));return e=>{if(e.length<=1)return e;let t=[],o=[];return e.forEach(e=>{"["===e[0]||r[e]?(t.push(...o.sort(),e),o=[]):o.push(e)}),t.push(...o.sort()),t}},k=e=>({cache:x(e.cacheSize),parseClassName:g(e),sortModifiers:y(e),...d(e)}),w=/\s+/,j=(e,r)=>{let{parseClassName:t,getClassGroupId:o,getConflictingClassGroupIds:a,sortModifiers:s}=r,n=[],l=e.trim().split(w),i="";for(let e=l.length-1;e>=0;e-=1){let r=l[e],{isExternal:d,modifiers:c,hasImportantModifier:m,baseClassName:p,maybePostfixModifierPosition:u}=t(r);if(d){i=r+(i.length>0?" "+i:i);continue}let h=!!u,f=o(h?p.substring(0,u):p);if(!f){if(!h||!(f=o(p))){i=r+(i.length>0?" "+i:i);continue}h=!1}let b=s(c).join(":"),x=m?b+"!":b,g=x+f;if(n.includes(g))continue;n.push(g);let v=a(f,h);for(let e=0;e<v.length;++e){let r=v[e];n.push(x+r)}i=r+(i.length>0?" "+i:i)}return i};function N(){let e,r,t=0,o="";for(;t<arguments.length;)(e=arguments[t++])&&(r=z(e))&&(o&&(o+=" "),o+=r);return o}let z=e=>{let r;if("string"==typeof e)return e;let t="";for(let o=0;o<e.length;o++)e[o]&&(r=z(e[o]))&&(t&&(t+=" "),t+=r);return t},P=e=>{let r=r=>r[e]||[];return r.isThemeGetter=!0,r},M=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,C=/^\((?:(\w[\w-]*):)?(.+)\)$/i,E=/^\d+\/\d+$/,A=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,G=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,_=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,q=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,D=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,$=e=>E.test(e),S=e=>!!e&&!Number.isNaN(Number(e)),R=e=>!!e&&Number.isInteger(Number(e)),I=e=>e.endsWith("%")&&S(e.slice(0,-1)),O=e=>A.test(e),T=()=>!0,V=e=>G.test(e)&&!_.test(e),W=()=>!1,H=e=>q.test(e),U=e=>D.test(e),L=e=>!F(e)&&!Q(e),B=e=>en(e,ec,W),F=e=>M.test(e),Z=e=>en(e,em,V),J=e=>en(e,ep,S),K=e=>en(e,ei,W),X=e=>en(e,ed,U),Y=e=>en(e,eh,H),Q=e=>C.test(e),ee=e=>el(e,em),er=e=>el(e,eu),et=e=>el(e,ei),eo=e=>el(e,ec),ea=e=>el(e,ed),es=e=>el(e,eh,!0),en=(e,r,t)=>{let o=M.exec(e);return!!o&&(o[1]?r(o[1]):t(o[2]))},el=(e,r,t=!1)=>{let o=C.exec(e);return!!o&&(o[1]?r(o[1]):t)},ei=e=>"position"===e||"percentage"===e,ed=e=>"image"===e||"url"===e,ec=e=>"length"===e||"size"===e||"bg-size"===e,em=e=>"length"===e,ep=e=>"number"===e,eu=e=>"family-name"===e,eh=e=>"shadow"===e;Symbol.toStringTag;let ef=function(e,...r){let t,o,a,s=function(l){return o=(t=k(r.reduce((e,r)=>r(e),e()))).cache.get,a=t.cache.set,s=n,n(l)};function n(e){let r=o(e);if(r)return r;let s=j(e,t);return a(e,s),s}return function(){return s(N.apply(null,arguments))}}(()=>{let e=P("color"),r=P("font"),t=P("text"),o=P("font-weight"),a=P("tracking"),s=P("leading"),n=P("breakpoint"),l=P("container"),i=P("spacing"),d=P("radius"),c=P("shadow"),m=P("inset-shadow"),p=P("text-shadow"),u=P("drop-shadow"),h=P("blur"),f=P("perspective"),b=P("aspect"),x=P("ease"),g=P("animate"),v=()=>["auto","avoid","all","avoid-page","page","left","right","column"],y=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],k=()=>[...y(),Q,F],w=()=>["auto","hidden","clip","visible","scroll"],j=()=>["auto","contain","none"],N=()=>[Q,F,i],z=()=>[$,"full","auto",...N()],M=()=>[R,"none","subgrid",Q,F],C=()=>["auto",{span:["full",R,Q,F]},R,Q,F],E=()=>[R,"auto",Q,F],A=()=>["auto","min","max","fr",Q,F],G=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],_=()=>["start","end","center","stretch","center-safe","end-safe"],q=()=>["auto",...N()],D=()=>[$,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...N()],V=()=>[e,Q,F],W=()=>[...y(),et,K,{position:[Q,F]}],H=()=>["no-repeat",{repeat:["","x","y","space","round"]}],U=()=>["auto","cover","contain",eo,B,{size:[Q,F]}],en=()=>[I,ee,Z],el=()=>["","none","full",d,Q,F],ei=()=>["",S,ee,Z],ed=()=>["solid","dashed","dotted","double"],ec=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],em=()=>[S,I,et,K],ep=()=>["","none",h,Q,F],eu=()=>["none",S,Q,F],eh=()=>["none",S,Q,F],ef=()=>[S,Q,F],eb=()=>[$,"full",...N()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[O],breakpoint:[O],color:[T],container:[O],"drop-shadow":[O],ease:["in","out","in-out"],font:[L],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[O],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[O],shadow:[O],spacing:["px",S],text:[O],"text-shadow":[O],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",$,F,Q,b]}],container:["container"],columns:[{columns:[S,F,Q,l]}],"break-after":[{"break-after":v()}],"break-before":[{"break-before":v()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:k()}],overflow:[{overflow:w()}],"overflow-x":[{"overflow-x":w()}],"overflow-y":[{"overflow-y":w()}],overscroll:[{overscroll:j()}],"overscroll-x":[{"overscroll-x":j()}],"overscroll-y":[{"overscroll-y":j()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:z()}],"inset-x":[{"inset-x":z()}],"inset-y":[{"inset-y":z()}],start:[{start:z()}],end:[{end:z()}],top:[{top:z()}],right:[{right:z()}],bottom:[{bottom:z()}],left:[{left:z()}],visibility:["visible","invisible","collapse"],z:[{z:[R,"auto",Q,F]}],basis:[{basis:[$,"full","auto",l,...N()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[S,$,"auto","initial","none",F]}],grow:[{grow:["",S,Q,F]}],shrink:[{shrink:["",S,Q,F]}],order:[{order:[R,"first","last","none",Q,F]}],"grid-cols":[{"grid-cols":M()}],"col-start-end":[{col:C()}],"col-start":[{"col-start":E()}],"col-end":[{"col-end":E()}],"grid-rows":[{"grid-rows":M()}],"row-start-end":[{row:C()}],"row-start":[{"row-start":E()}],"row-end":[{"row-end":E()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":A()}],"auto-rows":[{"auto-rows":A()}],gap:[{gap:N()}],"gap-x":[{"gap-x":N()}],"gap-y":[{"gap-y":N()}],"justify-content":[{justify:[...G(),"normal"]}],"justify-items":[{"justify-items":[..._(),"normal"]}],"justify-self":[{"justify-self":["auto",..._()]}],"align-content":[{content:["normal",...G()]}],"align-items":[{items:[..._(),{baseline:["","last"]}]}],"align-self":[{self:["auto",..._(),{baseline:["","last"]}]}],"place-content":[{"place-content":G()}],"place-items":[{"place-items":[..._(),"baseline"]}],"place-self":[{"place-self":["auto",..._()]}],p:[{p:N()}],px:[{px:N()}],py:[{py:N()}],ps:[{ps:N()}],pe:[{pe:N()}],pt:[{pt:N()}],pr:[{pr:N()}],pb:[{pb:N()}],pl:[{pl:N()}],m:[{m:q()}],mx:[{mx:q()}],my:[{my:q()}],ms:[{ms:q()}],me:[{me:q()}],mt:[{mt:q()}],mr:[{mr:q()}],mb:[{mb:q()}],ml:[{ml:q()}],"space-x":[{"space-x":N()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":N()}],"space-y-reverse":["space-y-reverse"],size:[{size:D()}],w:[{w:[l,"screen",...D()]}],"min-w":[{"min-w":[l,"screen","none",...D()]}],"max-w":[{"max-w":[l,"screen","none","prose",{screen:[n]},...D()]}],h:[{h:["screen","lh",...D()]}],"min-h":[{"min-h":["screen","lh","none",...D()]}],"max-h":[{"max-h":["screen","lh",...D()]}],"font-size":[{text:["base",t,ee,Z]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[o,Q,J]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",I,F]}],"font-family":[{font:[er,F,r]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[a,Q,F]}],"line-clamp":[{"line-clamp":[S,"none",Q,J]}],leading:[{leading:[s,...N()]}],"list-image":[{"list-image":["none",Q,F]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",Q,F]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:V()}],"text-color":[{text:V()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...ed(),"wavy"]}],"text-decoration-thickness":[{decoration:[S,"from-font","auto",Q,Z]}],"text-decoration-color":[{decoration:V()}],"underline-offset":[{"underline-offset":[S,"auto",Q,F]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:N()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",Q,F]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",Q,F]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:W()}],"bg-repeat":[{bg:H()}],"bg-size":[{bg:U()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},R,Q,F],radial:["",Q,F],conic:[R,Q,F]},ea,X]}],"bg-color":[{bg:V()}],"gradient-from-pos":[{from:en()}],"gradient-via-pos":[{via:en()}],"gradient-to-pos":[{to:en()}],"gradient-from":[{from:V()}],"gradient-via":[{via:V()}],"gradient-to":[{to:V()}],rounded:[{rounded:el()}],"rounded-s":[{"rounded-s":el()}],"rounded-e":[{"rounded-e":el()}],"rounded-t":[{"rounded-t":el()}],"rounded-r":[{"rounded-r":el()}],"rounded-b":[{"rounded-b":el()}],"rounded-l":[{"rounded-l":el()}],"rounded-ss":[{"rounded-ss":el()}],"rounded-se":[{"rounded-se":el()}],"rounded-ee":[{"rounded-ee":el()}],"rounded-es":[{"rounded-es":el()}],"rounded-tl":[{"rounded-tl":el()}],"rounded-tr":[{"rounded-tr":el()}],"rounded-br":[{"rounded-br":el()}],"rounded-bl":[{"rounded-bl":el()}],"border-w":[{border:ei()}],"border-w-x":[{"border-x":ei()}],"border-w-y":[{"border-y":ei()}],"border-w-s":[{"border-s":ei()}],"border-w-e":[{"border-e":ei()}],"border-w-t":[{"border-t":ei()}],"border-w-r":[{"border-r":ei()}],"border-w-b":[{"border-b":ei()}],"border-w-l":[{"border-l":ei()}],"divide-x":[{"divide-x":ei()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":ei()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...ed(),"hidden","none"]}],"divide-style":[{divide:[...ed(),"hidden","none"]}],"border-color":[{border:V()}],"border-color-x":[{"border-x":V()}],"border-color-y":[{"border-y":V()}],"border-color-s":[{"border-s":V()}],"border-color-e":[{"border-e":V()}],"border-color-t":[{"border-t":V()}],"border-color-r":[{"border-r":V()}],"border-color-b":[{"border-b":V()}],"border-color-l":[{"border-l":V()}],"divide-color":[{divide:V()}],"outline-style":[{outline:[...ed(),"none","hidden"]}],"outline-offset":[{"outline-offset":[S,Q,F]}],"outline-w":[{outline:["",S,ee,Z]}],"outline-color":[{outline:V()}],shadow:[{shadow:["","none",c,es,Y]}],"shadow-color":[{shadow:V()}],"inset-shadow":[{"inset-shadow":["none",m,es,Y]}],"inset-shadow-color":[{"inset-shadow":V()}],"ring-w":[{ring:ei()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:V()}],"ring-offset-w":[{"ring-offset":[S,Z]}],"ring-offset-color":[{"ring-offset":V()}],"inset-ring-w":[{"inset-ring":ei()}],"inset-ring-color":[{"inset-ring":V()}],"text-shadow":[{"text-shadow":["none",p,es,Y]}],"text-shadow-color":[{"text-shadow":V()}],opacity:[{opacity:[S,Q,F]}],"mix-blend":[{"mix-blend":[...ec(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":ec()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[S]}],"mask-image-linear-from-pos":[{"mask-linear-from":em()}],"mask-image-linear-to-pos":[{"mask-linear-to":em()}],"mask-image-linear-from-color":[{"mask-linear-from":V()}],"mask-image-linear-to-color":[{"mask-linear-to":V()}],"mask-image-t-from-pos":[{"mask-t-from":em()}],"mask-image-t-to-pos":[{"mask-t-to":em()}],"mask-image-t-from-color":[{"mask-t-from":V()}],"mask-image-t-to-color":[{"mask-t-to":V()}],"mask-image-r-from-pos":[{"mask-r-from":em()}],"mask-image-r-to-pos":[{"mask-r-to":em()}],"mask-image-r-from-color":[{"mask-r-from":V()}],"mask-image-r-to-color":[{"mask-r-to":V()}],"mask-image-b-from-pos":[{"mask-b-from":em()}],"mask-image-b-to-pos":[{"mask-b-to":em()}],"mask-image-b-from-color":[{"mask-b-from":V()}],"mask-image-b-to-color":[{"mask-b-to":V()}],"mask-image-l-from-pos":[{"mask-l-from":em()}],"mask-image-l-to-pos":[{"mask-l-to":em()}],"mask-image-l-from-color":[{"mask-l-from":V()}],"mask-image-l-to-color":[{"mask-l-to":V()}],"mask-image-x-from-pos":[{"mask-x-from":em()}],"mask-image-x-to-pos":[{"mask-x-to":em()}],"mask-image-x-from-color":[{"mask-x-from":V()}],"mask-image-x-to-color":[{"mask-x-to":V()}],"mask-image-y-from-pos":[{"mask-y-from":em()}],"mask-image-y-to-pos":[{"mask-y-to":em()}],"mask-image-y-from-color":[{"mask-y-from":V()}],"mask-image-y-to-color":[{"mask-y-to":V()}],"mask-image-radial":[{"mask-radial":[Q,F]}],"mask-image-radial-from-pos":[{"mask-radial-from":em()}],"mask-image-radial-to-pos":[{"mask-radial-to":em()}],"mask-image-radial-from-color":[{"mask-radial-from":V()}],"mask-image-radial-to-color":[{"mask-radial-to":V()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":y()}],"mask-image-conic-pos":[{"mask-conic":[S]}],"mask-image-conic-from-pos":[{"mask-conic-from":em()}],"mask-image-conic-to-pos":[{"mask-conic-to":em()}],"mask-image-conic-from-color":[{"mask-conic-from":V()}],"mask-image-conic-to-color":[{"mask-conic-to":V()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:W()}],"mask-repeat":[{mask:H()}],"mask-size":[{mask:U()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",Q,F]}],filter:[{filter:["","none",Q,F]}],blur:[{blur:ep()}],brightness:[{brightness:[S,Q,F]}],contrast:[{contrast:[S,Q,F]}],"drop-shadow":[{"drop-shadow":["","none",u,es,Y]}],"drop-shadow-color":[{"drop-shadow":V()}],grayscale:[{grayscale:["",S,Q,F]}],"hue-rotate":[{"hue-rotate":[S,Q,F]}],invert:[{invert:["",S,Q,F]}],saturate:[{saturate:[S,Q,F]}],sepia:[{sepia:["",S,Q,F]}],"backdrop-filter":[{"backdrop-filter":["","none",Q,F]}],"backdrop-blur":[{"backdrop-blur":ep()}],"backdrop-brightness":[{"backdrop-brightness":[S,Q,F]}],"backdrop-contrast":[{"backdrop-contrast":[S,Q,F]}],"backdrop-grayscale":[{"backdrop-grayscale":["",S,Q,F]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[S,Q,F]}],"backdrop-invert":[{"backdrop-invert":["",S,Q,F]}],"backdrop-opacity":[{"backdrop-opacity":[S,Q,F]}],"backdrop-saturate":[{"backdrop-saturate":[S,Q,F]}],"backdrop-sepia":[{"backdrop-sepia":["",S,Q,F]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":N()}],"border-spacing-x":[{"border-spacing-x":N()}],"border-spacing-y":[{"border-spacing-y":N()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",Q,F]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[S,"initial",Q,F]}],ease:[{ease:["linear","initial",x,Q,F]}],delay:[{delay:[S,Q,F]}],animate:[{animate:["none",g,Q,F]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[f,Q,F]}],"perspective-origin":[{"perspective-origin":k()}],rotate:[{rotate:eu()}],"rotate-x":[{"rotate-x":eu()}],"rotate-y":[{"rotate-y":eu()}],"rotate-z":[{"rotate-z":eu()}],scale:[{scale:eh()}],"scale-x":[{"scale-x":eh()}],"scale-y":[{"scale-y":eh()}],"scale-z":[{"scale-z":eh()}],"scale-3d":["scale-3d"],skew:[{skew:ef()}],"skew-x":[{"skew-x":ef()}],"skew-y":[{"skew-y":ef()}],transform:[{transform:[Q,F,"","none","gpu","cpu"]}],"transform-origin":[{origin:k()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:eb()}],"translate-x":[{"translate-x":eb()}],"translate-y":[{"translate-y":eb()}],"translate-z":[{"translate-z":eb()}],"translate-none":["translate-none"],accent:[{accent:V()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:V()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",Q,F]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":N()}],"scroll-mx":[{"scroll-mx":N()}],"scroll-my":[{"scroll-my":N()}],"scroll-ms":[{"scroll-ms":N()}],"scroll-me":[{"scroll-me":N()}],"scroll-mt":[{"scroll-mt":N()}],"scroll-mr":[{"scroll-mr":N()}],"scroll-mb":[{"scroll-mb":N()}],"scroll-ml":[{"scroll-ml":N()}],"scroll-p":[{"scroll-p":N()}],"scroll-px":[{"scroll-px":N()}],"scroll-py":[{"scroll-py":N()}],"scroll-ps":[{"scroll-ps":N()}],"scroll-pe":[{"scroll-pe":N()}],"scroll-pt":[{"scroll-pt":N()}],"scroll-pr":[{"scroll-pr":N()}],"scroll-pb":[{"scroll-pb":N()}],"scroll-pl":[{"scroll-pl":N()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",Q,F]}],fill:[{fill:["none",...V()]}],"stroke-w":[{stroke:[S,ee,Z,J]}],stroke:[{stroke:["none",...V()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}});function eb(...e){return ef(l(e))}let ex=((e,r)=>t=>{var o;if((null==r?void 0:r.variants)==null)return l(e,null==t?void 0:t.class,null==t?void 0:t.className);let{variants:a,defaultVariants:s}=r,n=Object.keys(a).map(e=>{let r=null==t?void 0:t[e],o=null==s?void 0:s[e];if(null===r)return null;let n=i(r)||i(o);return a[e][n]}),d=t&&Object.entries(t).reduce((e,r)=>{let[t,o]=r;return void 0===o||(e[t]=o),e},{});return l(e,n,null==r||null==(o=r.compoundVariants)?void 0:o.reduce((e,r)=>{let{class:t,className:o,...a}=r;return Object.entries(a).every(e=>{let[r,t]=e;return Array.isArray(t)?t.includes({...s,...d}[r]):({...s,...d})[r]===t})?[...e,t,o]:e},[]),null==t?void 0:t.class,null==t?void 0:t.className)})("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),eg=n.forwardRef(({className:e,variant:r,size:t,...a},s)=>(0,o.jsx)("button",{className:eb(ex({variant:r,size:t,className:e})),ref:s,...a}));eg.displayName="Button";let ev=n.forwardRef(({className:e,...r},t)=>(0,o.jsx)("div",{ref:t,className:eb("rounded-lg border bg-card text-card-foreground shadow-sm",e),...r}));ev.displayName="Card";let ey=n.forwardRef(({className:e,...r},t)=>(0,o.jsx)("div",{ref:t,className:eb("flex flex-col space-y-1.5 p-6",e),...r}));ey.displayName="CardHeader";let ek=n.forwardRef(({className:e,...r},t)=>(0,o.jsx)("h3",{ref:t,className:eb("text-2xl font-semibold leading-none tracking-tight",e),...r}));ek.displayName="CardTitle";let ew=n.forwardRef(({className:e,...r},t)=>(0,o.jsx)("p",{ref:t,className:eb("text-sm text-muted-foreground",e),...r}));ew.displayName="CardDescription",n.forwardRef(({className:e,...r},t)=>(0,o.jsx)("div",{ref:t,className:eb("p-6 pt-0",e),...r})).displayName="CardContent",n.forwardRef(({className:e,...r},t)=>(0,o.jsx)("div",{ref:t,className:eb("flex items-center p-6 pt-0",e),...r})).displayName="CardFooter";let ej=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),eN=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,r,t)=>t?t.toUpperCase():r.toLowerCase()),ez=e=>{let r=eN(e);return r.charAt(0).toUpperCase()+r.slice(1)},eP=(...e)=>e.filter((e,r,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===r).join(" ").trim(),eM=e=>{for(let r in e)if(r.startsWith("aria-")||"role"===r||"title"===r)return!0};var eC={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let eE=(0,n.forwardRef)(({color:e="currentColor",size:r=24,strokeWidth:t=2,absoluteStrokeWidth:o,className:a="",children:s,iconNode:l,...i},d)=>(0,n.createElement)("svg",{ref:d,...eC,width:r,height:r,stroke:e,strokeWidth:o?24*Number(t)/Number(r):t,className:eP("lucide",a),...!s&&!eM(i)&&{"aria-hidden":"true"},...i},[...l.map(([e,r])=>(0,n.createElement)(e,r)),...Array.isArray(s)?s:[s]])),eA=(e,r)=>{let t=(0,n.forwardRef)(({className:t,...o},a)=>(0,n.createElement)(eE,{ref:a,iconNode:r,className:eP(`lucide-${ej(ez(e))}`,`lucide-${e}`,t),...o}));return t.displayName=ez(e),t},eG=eA("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]]),e_=eA("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]),eq=eA("lightbulb",[["path",{d:"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 3.5.7.7 1.3 1.5 1.5 2.5",key:"1gvzjb"}],["path",{d:"M9 18h6",key:"x1upvd"}],["path",{d:"M10 22h4",key:"ceow96"}]]),eD=eA("calculator",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",key:"1nb95v"}],["line",{x1:"8",x2:"16",y1:"6",y2:"6",key:"x4nwl0"}],["line",{x1:"16",x2:"16",y1:"14",y2:"18",key:"wjye3r"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M8 14h.01",key:"6423bh"}],["path",{d:"M12 18h.01",key:"mhygvu"}],["path",{d:"M8 18h.01",key:"lrp35t"}]]),e$=eA("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]]),eS=eA("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]),eR=eA("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),eI=eA("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]);function eO(){return(0,o.jsxs)("div",{className:"flex flex-col min-h-screen",children:[(0,o.jsx)("header",{className:"border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",children:(0,o.jsxs)("div",{className:"container flex h-16 items-center justify-between",children:[(0,o.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,o.jsx)(eG,{className:"h-6 w-6 text-primary"}),(0,o.jsx)("span",{className:"text-xl font-bold",children:"ElectriPanel Pro"})]}),(0,o.jsxs)("nav",{className:"hidden md:flex items-center space-x-6",children:[(0,o.jsx)(s(),{href:"#features",className:"text-sm font-medium hover:text-primary",children:"Features"}),(0,o.jsx)(s(),{href:"#pricing",className:"text-sm font-medium hover:text-primary",children:"Pricing"}),(0,o.jsx)(s(),{href:"#about",className:"text-sm font-medium hover:text-primary",children:"About"})]}),(0,o.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,o.jsx)(eg,{variant:"ghost",asChild:!0,children:(0,o.jsx)(s(),{href:"/login",children:"Sign In"})}),(0,o.jsx)(eg,{asChild:!0,children:(0,o.jsx)(s(),{href:"/dashboard",children:"Get Started"})})]})]})}),(0,o.jsx)("section",{className:"flex-1 flex items-center justify-center py-20 px-4",children:(0,o.jsx)("div",{className:"container max-w-6xl text-center",children:(0,o.jsxs)("div",{className:"mx-auto max-w-3xl",children:[(0,o.jsx)("h1",{className:"text-4xl font-bold tracking-tight sm:text-6xl mb-6",children:"Professional Electrical Panel Design & Management"}),(0,o.jsx)("p",{className:"text-lg text-muted-foreground mb-8 max-w-2xl mx-auto",children:"Streamline your electrical panel board design, planning, and documentation. Built for electricians, engineers, and contractors who demand precision and compliance."}),(0,o.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,o.jsx)(eg,{size:"lg",asChild:!0,children:(0,o.jsxs)(s(),{href:"/dashboard",children:["Start Designing ",(0,o.jsx)(e_,{className:"ml-2 h-4 w-4"})]})}),(0,o.jsx)(eg,{size:"lg",variant:"outline",asChild:!0,children:(0,o.jsx)(s(),{href:"#features",children:"Learn More"})})]})]})})}),(0,o.jsx)("section",{id:"features",className:"py-20 bg-muted/50",children:(0,o.jsxs)("div",{className:"container max-w-6xl",children:[(0,o.jsxs)("div",{className:"text-center mb-16",children:[(0,o.jsx)("h2",{className:"text-3xl font-bold mb-4",children:"Powerful Features for Electrical Professionals"}),(0,o.jsx)("p",{className:"text-lg text-muted-foreground max-w-2xl mx-auto",children:"Everything you need to design, calculate, and document electrical panel boards with confidence."})]}),(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:[(0,o.jsx)(ev,{children:(0,o.jsxs)(ey,{children:[(0,o.jsx)(eq,{className:"h-8 w-8 text-primary mb-2"}),(0,o.jsx)(ek,{children:"Interactive Design"}),(0,o.jsx)(ew,{children:"Drag-and-drop panel layout builder with 2D/3D visualization"})]})}),(0,o.jsx)(ev,{children:(0,o.jsxs)(ey,{children:[(0,o.jsx)(eD,{className:"h-8 w-8 text-primary mb-2"}),(0,o.jsx)(ek,{children:"Smart Calculations"}),(0,o.jsx)(ew,{children:"Automated load calculations, voltage drop analysis, and wire sizing per NEC Article 220"})]})}),(0,o.jsx)(ev,{children:(0,o.jsxs)(ey,{children:[(0,o.jsx)(e$,{className:"h-8 w-8 text-primary mb-2"}),(0,o.jsx)(ek,{children:"Code Compliance"}),(0,o.jsx)(ew,{children:"Real-time compliance checking with built-in NEC, IEC, and local code references"})]})}),(0,o.jsx)(ev,{children:(0,o.jsxs)(ey,{children:[(0,o.jsx)(eS,{className:"h-8 w-8 text-primary mb-2"}),(0,o.jsx)(ek,{children:"Documentation"}),(0,o.jsx)(ew,{children:"Generate permit-ready panel schedules, material lists, and specifications"})]})}),(0,o.jsx)(ev,{children:(0,o.jsxs)(ey,{children:[(0,o.jsx)(eR,{className:"h-8 w-8 text-primary mb-2"}),(0,o.jsx)(ek,{children:"Component Database"}),(0,o.jsx)(ew,{children:"Searchable database of manufacturers' components with compatibility checking"})]})}),(0,o.jsx)(ev,{children:(0,o.jsxs)(ey,{children:[(0,o.jsx)(eI,{className:"h-8 w-8 text-primary mb-2"}),(0,o.jsx)(ek,{children:"Project Management"}),(0,o.jsx)(ew,{children:"Track projects, collaborate with teams, and maintain version history"})]})})]})]})}),(0,o.jsx)("section",{className:"py-20",children:(0,o.jsxs)("div",{className:"container max-w-4xl text-center",children:[(0,o.jsx)("h2",{className:"text-3xl font-bold mb-4",children:"Ready to Transform Your Electrical Design Process?"}),(0,o.jsx)("p",{className:"text-lg text-muted-foreground mb-8",children:"Join thousands of electrical professionals who trust ElectriPanel Pro for their panel design needs."}),(0,o.jsx)(eg,{size:"lg",asChild:!0,children:(0,o.jsxs)(s(),{href:"/dashboard",children:["Get Started Today ",(0,o.jsx)(e_,{className:"ml-2 h-4 w-4"})]})})]})}),(0,o.jsx)("footer",{className:"border-t bg-muted/50",children:(0,o.jsx)("div",{className:"container py-8",children:(0,o.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center",children:[(0,o.jsxs)("div",{className:"flex items-center space-x-2 mb-4 md:mb-0",children:[(0,o.jsx)(eG,{className:"h-5 w-5 text-primary"}),(0,o.jsx)("span",{className:"font-semibold",children:"ElectriPanel Pro"})]}),(0,o.jsx)("p",{className:"text-sm text-muted-foreground",children:"\xa9 2024 ElectriPanel Pro. All rights reserved."})]})})})]})}},8950:()=>{},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")}};var r=require("../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[447,423,567],()=>t(6507));module.exports=o})();