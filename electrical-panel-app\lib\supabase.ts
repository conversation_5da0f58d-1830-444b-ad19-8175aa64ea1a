import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Database schema types (will be generated from Supabase)
export type Database = {
  public: {
    Tables: {
      users: {
        Row: {
          id: string
          email: string
          name: string
          role: 'electrician' | 'engineer' | 'contractor' | 'inspector'
          company: string | null
          license_number: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          email: string
          name: string
          role: 'electrician' | 'engineer' | 'contractor' | 'inspector'
          company?: string | null
          license_number?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          name?: string
          role?: 'electrician' | 'engineer' | 'contractor' | 'inspector'
          company?: string | null
          license_number?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      projects: {
        Row: {
          id: string
          name: string
          description: string | null
          location: string
          user_id: string
          status: 'draft' | 'in_progress' | 'review' | 'approved' | 'completed'
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          description?: string | null
          location: string
          user_id: string
          status?: 'draft' | 'in_progress' | 'review' | 'approved' | 'completed'
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          location?: string
          user_id?: string
          status?: 'draft' | 'in_progress' | 'review' | 'approved' | 'completed'
          created_at?: string
          updated_at?: string
        }
      }
      panels: {
        Row: {
          id: string
          project_id: string
          name: string
          type: 'main' | 'sub' | 'distribution'
          voltage: number
          phases: number
          main_breaker_size: number
          bus_rating: number
          short_circuit_rating: number
          location: string
          manufacturer: string | null
          model: string | null
          position_x: number | null
          position_y: number | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          project_id: string
          name: string
          type: 'main' | 'sub' | 'distribution'
          voltage: number
          phases: number
          main_breaker_size: number
          bus_rating: number
          short_circuit_rating: number
          location: string
          manufacturer?: string | null
          model?: string | null
          position_x?: number | null
          position_y?: number | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          project_id?: string
          name?: string
          type?: 'main' | 'sub' | 'distribution'
          voltage?: number
          phases?: number
          main_breaker_size?: number
          bus_rating?: number
          short_circuit_rating?: number
          location?: string
          manufacturer?: string | null
          model?: string | null
          position_x?: number | null
          position_y?: number | null
          created_at?: string
          updated_at?: string
        }
      }
      circuits: {
        Row: {
          id: string
          panel_id: string
          circuit_number: number
          description: string
          load_type: 'lighting' | 'receptacle' | 'motor' | 'hvac' | 'appliance' | 'other'
          voltage: number
          amperage: number
          wire_size: string
          breaker_size: number
          breaker_type: 'single' | 'double' | 'triple'
          phase: string
          distance: number
          notes: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          panel_id: string
          circuit_number: number
          description: string
          load_type: 'lighting' | 'receptacle' | 'motor' | 'hvac' | 'appliance' | 'other'
          voltage: number
          amperage: number
          wire_size: string
          breaker_size: number
          breaker_type: 'single' | 'double' | 'triple'
          phase: string
          distance: number
          notes?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          panel_id?: string
          circuit_number?: number
          description?: string
          load_type?: 'lighting' | 'receptacle' | 'motor' | 'hvac' | 'appliance' | 'other'
          voltage?: number
          amperage?: number
          wire_size?: string
          breaker_size?: number
          breaker_type?: 'single' | 'double' | 'triple'
          phase?: string
          distance?: number
          notes?: string | null
          created_at?: string
          updated_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
  }
}
