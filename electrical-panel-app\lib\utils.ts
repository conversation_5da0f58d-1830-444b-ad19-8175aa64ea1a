import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// Electrical calculation utilities
export const ElectricalConstants = {
  // Standard voltages
  VOLTAGE_120: 120,
  VOLTAGE_240: 240,
  VOLTAGE_208: 208,
  VOLTAGE_480: 480,
  
  // Wire resistance per 1000ft (ohms) for copper conductors at 75°C
  WIRE_RESISTANCE: {
    '14': 3.07,
    '12': 1.93,
    '10': 1.21,
    '8': 0.764,
    '6': 0.491,
    '4': 0.308,
    '3': 0.245,
    '2': 0.194,
    '1': 0.154,
    '1/0': 0.122,
    '2/0': 0.0967,
    '3/0': 0.0766,
    '4/0': 0.0608,
  },
  
  // Ampacity ratings for copper conductors (75°C)
  WIRE_AMPACITY: {
    '14': 20,
    '12': 25,
    '10': 35,
    '8': 50,
    '6': 65,
    '4': 85,
    '3': 100,
    '2': 115,
    '1': 130,
    '1/0': 150,
    '2/0': 175,
    '3/0': 200,
    '4/0': 230,
  }
}

// Calculate voltage drop
export function calculateVoltageDrop(
  current: number,
  distance: number,
  wireSize: string,
  voltage: number = 240
): number {
  const resistance = ElectricalConstants.WIRE_RESISTANCE[wireSize as keyof typeof ElectricalConstants.WIRE_RESISTANCE]
  if (!resistance) return 0
  
  // Voltage drop = 2 * I * R * L / 1000 (for single phase)
  const voltageDrop = (2 * current * resistance * distance) / 1000
  return Math.round((voltageDrop / voltage) * 100 * 100) / 100 // Percentage with 2 decimal places
}

// Calculate wire size needed for given load
export function calculateWireSize(current: number, distance: number, maxVoltageDrop: number = 3): string {
  const wireSizes = Object.keys(ElectricalConstants.WIRE_AMPACITY)
  
  for (const size of wireSizes) {
    const ampacity = ElectricalConstants.WIRE_AMPACITY[size as keyof typeof ElectricalConstants.WIRE_AMPACITY]
    if (ampacity >= current * 1.25) { // 125% rule
      const voltageDrop = calculateVoltageDrop(current, distance, size)
      if (voltageDrop <= maxVoltageDrop) {
        return size
      }
    }
  }
  
  return '4/0' // Largest standard size
}

// Format currency
export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
  }).format(amount)
}

// Format date
export function formatDate(date: Date): string {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  }).format(date)
}
