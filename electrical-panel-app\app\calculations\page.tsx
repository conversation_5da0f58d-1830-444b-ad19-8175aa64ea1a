"use client";

import { useState } from "react";
import Link from "next/link";
import { But<PERSON> } from "../../components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "../../components/ui/card";
import { 
  <PERSON><PERSON>,
  Calculator,
  ArrowLeft,
  Settings,
  User
} from "lucide-react";
import { calculateVoltageDrop, calculateWireSize, ElectricalConstants } from "../../lib/utils";

export default function Calculations() {
  const [current, setCurrent] = useState<number>(20);
  const [distance, setDistance] = useState<number>(100);
  const [voltage, setVoltage] = useState<number>(240);
  const [wireSize, setWireSize] = useState<string>("12");
  const [maxVoltageDrop, setMaxVoltageDrop] = useState<number>(3);

  const voltageDrop = calculateVoltageDrop(current, distance, wireSize, voltage);
  const recommendedWireSize = calculateWireSize(current, distance, maxVoltageDrop);
  const wireAmpacity = ElectricalConstants.WIRE_AMPACITY[wireSize as keyof typeof ElectricalConstants.WIRE_AMPACITY];

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container flex h-16 items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button variant="ghost" size="icon" asChild>
              <Link href="/dashboard">
                <ArrowLeft className="h-4 w-4" />
              </Link>
            </Button>
            <div className="flex items-center space-x-2">
              <Zap className="h-6 w-6 text-primary" />
              <span className="text-xl font-bold">ElectriPanel Pro</span>
            </div>
          </div>
          <nav className="hidden md:flex items-center space-x-6">
            <Link href="/dashboard" className="text-sm font-medium hover:text-primary">
              Dashboard
            </Link>
            <Link href="/projects" className="text-sm font-medium hover:text-primary">
              Projects
            </Link>
            <Link href="/design" className="text-sm font-medium hover:text-primary">
              Design
            </Link>
            <Link href="/calculations" className="text-sm font-medium text-primary">
              Calculations
            </Link>
          </nav>
          <div className="flex items-center space-x-4">
            <Button variant="ghost" size="icon">
              <Settings className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="icon">
              <User className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </header>

      <div className="container py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2 flex items-center">
            <Calculator className="h-8 w-8 mr-3 text-primary" />
            Electrical Calculations
          </h1>
          <p className="text-muted-foreground">
            Calculate voltage drop, wire sizing, and load requirements for your electrical projects.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Input Panel */}
          <Card>
            <CardHeader>
              <CardTitle>Circuit Parameters</CardTitle>
              <CardDescription>
                Enter the circuit parameters to calculate voltage drop and wire sizing
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium mb-2 block">Current (Amps)</label>
                  <input
                    type="number"
                    value={current}
                    onChange={(e) => setCurrent(Number(e.target.value))}
                    className="w-full px-3 py-2 border border-input rounded-md bg-background"
                    min="0"
                    step="0.1"
                  />
                </div>
                <div>
                  <label className="text-sm font-medium mb-2 block">Distance (feet)</label>
                  <input
                    type="number"
                    value={distance}
                    onChange={(e) => setDistance(Number(e.target.value))}
                    className="w-full px-3 py-2 border border-input rounded-md bg-background"
                    min="0"
                    step="1"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium mb-2 block">Voltage (V)</label>
                  <select
                    value={voltage}
                    onChange={(e) => setVoltage(Number(e.target.value))}
                    className="w-full px-3 py-2 border border-input rounded-md bg-background"
                  >
                    <option value={120}>120V</option>
                    <option value={208}>208V</option>
                    <option value={240}>240V</option>
                    <option value={480}>480V</option>
                  </select>
                </div>
                <div>
                  <label className="text-sm font-medium mb-2 block">Wire Size (AWG)</label>
                  <select
                    value={wireSize}
                    onChange={(e) => setWireSize(e.target.value)}
                    className="w-full px-3 py-2 border border-input rounded-md bg-background"
                  >
                    {Object.keys(ElectricalConstants.WIRE_AMPACITY).map((size) => (
                      <option key={size} value={size}>{size} AWG</option>
                    ))}
                  </select>
                </div>
              </div>

              <div>
                <label className="text-sm font-medium mb-2 block">Max Voltage Drop (%)</label>
                <input
                  type="number"
                  value={maxVoltageDrop}
                  onChange={(e) => setMaxVoltageDrop(Number(e.target.value))}
                  className="w-full px-3 py-2 border border-input rounded-md bg-background"
                  min="0"
                  max="10"
                  step="0.1"
                />
              </div>
            </CardContent>
          </Card>

          {/* Results Panel */}
          <Card>
            <CardHeader>
              <CardTitle>Calculation Results</CardTitle>
              <CardDescription>
                Based on your input parameters
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 gap-4">
                <div className="p-4 border rounded-lg">
                  <div className="text-sm font-medium text-muted-foreground mb-1">Voltage Drop</div>
                  <div className="text-2xl font-bold">
                    {voltageDrop}%
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {voltageDrop <= 3 ? "✓ Within acceptable limits" : "⚠ Exceeds 3% recommendation"}
                  </div>
                </div>

                <div className="p-4 border rounded-lg">
                  <div className="text-sm font-medium text-muted-foreground mb-1">Wire Ampacity</div>
                  <div className="text-2xl font-bold">
                    {wireAmpacity} A
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {wireAmpacity >= current * 1.25 ? "✓ Adequate for load" : "⚠ Undersized for load"}
                  </div>
                </div>

                <div className="p-4 border rounded-lg">
                  <div className="text-sm font-medium text-muted-foreground mb-1">Recommended Wire Size</div>
                  <div className="text-2xl font-bold">
                    {recommendedWireSize} AWG
                  </div>
                  <div className="text-sm text-muted-foreground">
                    Based on {maxVoltageDrop}% max voltage drop
                  </div>
                </div>

                <div className="p-4 border rounded-lg">
                  <div className="text-sm font-medium text-muted-foreground mb-1">Load Factor</div>
                  <div className="text-2xl font-bold">
                    {wireAmpacity ? Math.round((current / wireAmpacity) * 100) : 0}%
                  </div>
                  <div className="text-sm text-muted-foreground">
                    Wire utilization
                  </div>
                </div>
              </div>

              <div className="pt-4 border-t">
                <h4 className="font-medium mb-2">NEC References</h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• Article 210.19(A) - Branch Circuit Conductors</li>
                  <li>• Article 215.2 - Feeder Conductors</li>
                  <li>• Table 310.15(B)(16) - Ampacity Ratings</li>
                </ul>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Additional Calculation Tools */}
        <div className="mt-12">
          <h2 className="text-2xl font-bold mb-6">Additional Tools</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card className="cursor-pointer hover:shadow-md transition-shadow">
              <CardHeader>
                <CardTitle className="text-lg">Load Calculation</CardTitle>
                <CardDescription>
                  Calculate total connected and demand loads per NEC Article 220
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="cursor-pointer hover:shadow-md transition-shadow">
              <CardHeader>
                <CardTitle className="text-lg">Short Circuit Analysis</CardTitle>
                <CardDescription>
                  Determine fault current levels and protective device coordination
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="cursor-pointer hover:shadow-md transition-shadow">
              <CardHeader>
                <CardTitle className="text-lg">Conduit Fill</CardTitle>
                <CardDescription>
                  Calculate conduit fill percentages and sizing requirements
                </CardDescription>
              </CardHeader>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
