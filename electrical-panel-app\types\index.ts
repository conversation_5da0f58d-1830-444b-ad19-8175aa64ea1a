// Core types for the electrical panel application

export interface User {
  id: string
  email: string
  name: string
  role: 'electrician' | 'engineer' | 'contractor' | 'inspector'
  company?: string
  license_number?: string
  created_at: string
  updated_at: string
}

export interface Project {
  id: string
  name: string
  description?: string
  location: string
  user_id: string
  status: 'draft' | 'in_progress' | 'review' | 'approved' | 'completed'
  created_at: string
  updated_at: string
  panels: Panel[]
}

export interface Panel {
  id: string
  project_id: string
  name: string
  type: 'main' | 'sub' | 'distribution'
  voltage: number
  phases: 1 | 3
  main_breaker_size: number
  bus_rating: number
  short_circuit_rating: number
  location: string
  manufacturer?: string
  model?: string
  created_at: string
  updated_at: string
  circuits: Circuit[]
  position_x?: number
  position_y?: number
}

export interface Circuit {
  id: string
  panel_id: string
  circuit_number: number
  description: string
  load_type: 'lighting' | 'receptacle' | 'motor' | 'hvac' | 'appliance' | 'other'
  voltage: number
  amperage: number
  wire_size: string
  breaker_size: number
  breaker_type: 'single' | 'double' | 'triple'
  phase: 'A' | 'B' | 'C' | 'AB' | 'BC' | 'AC' | 'ABC'
  distance: number
  notes?: string
  created_at: string
  updated_at: string
}

export interface Component {
  id: string
  name: string
  manufacturer: string
  model: string
  type: 'breaker' | 'panel' | 'wire' | 'conduit' | 'connector' | 'meter'
  specifications: Record<string, any>
  price?: number
  availability: boolean
  created_at: string
  updated_at: string
}

export interface LoadCalculation {
  id: string
  project_id: string
  calculation_type: 'nec_220' | 'demand_factor' | 'custom'
  total_connected_load: number
  demand_load: number
  diversity_factor: number
  safety_factor: number
  calculated_load: number
  notes?: string
  created_at: string
  updated_at: string
}

export interface CodeCompliance {
  id: string
  project_id: string
  code_type: 'nec' | 'iec' | 'local'
  code_version: string
  violations: CodeViolation[]
  compliance_status: 'compliant' | 'violations' | 'warnings'
  last_checked: string
}

export interface CodeViolation {
  id: string
  rule_reference: string
  severity: 'error' | 'warning' | 'info'
  description: string
  location: string
  suggested_fix?: string
}

export interface PanelSchedule {
  id: string
  panel_id: string
  generated_at: string
  format: 'pdf' | 'excel' | 'autocad'
  file_url?: string
}

// UI State types
export interface PanelDesignState {
  selectedPanel: Panel | null
  selectedCircuit: Circuit | null
  viewMode: '2d' | '3d' | 'schedule'
  showCalculations: boolean
  showCompliance: boolean
}

// Form types
export interface CreateProjectForm {
  name: string
  description?: string
  location: string
}

export interface CreatePanelForm {
  name: string
  type: Panel['type']
  voltage: number
  phases: 1 | 3
  main_breaker_size: number
  bus_rating: number
  location: string
  manufacturer?: string
  model?: string
}

export interface CreateCircuitForm {
  circuit_number: number
  description: string
  load_type: Circuit['load_type']
  voltage: number
  amperage: number
  breaker_type: Circuit['breaker_type']
  phase: Circuit['phase']
  distance: number
  notes?: string
}

// API Response types
export interface ApiResponse<T> {
  data: T
  error?: string
  message?: string
}

export interface PaginatedResponse<T> {
  data: T[]
  total: number
  page: number
  limit: number
  hasMore: boolean
}
