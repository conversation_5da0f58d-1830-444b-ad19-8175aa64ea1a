[{"C:\\Users\\<USER>\\Desktop\\New folder (16)\\electrical-panel-app\\src\\app\\calculations\\page.tsx": "1", "C:\\Users\\<USER>\\Desktop\\New folder (16)\\electrical-panel-app\\src\\app\\dashboard\\page.tsx": "2", "C:\\Users\\<USER>\\Desktop\\New folder (16)\\electrical-panel-app\\src\\app\\layout.tsx": "3", "C:\\Users\\<USER>\\Desktop\\New folder (16)\\electrical-panel-app\\src\\app\\page.tsx": "4", "C:\\Users\\<USER>\\Desktop\\New folder (16)\\electrical-panel-app\\src\\components\\ui\\button.tsx": "5", "C:\\Users\\<USER>\\Desktop\\New folder (16)\\electrical-panel-app\\src\\components\\ui\\card.tsx": "6", "C:\\Users\\<USER>\\Desktop\\New folder (16)\\electrical-panel-app\\src\\lib\\supabase.ts": "7", "C:\\Users\\<USER>\\Desktop\\New folder (16)\\electrical-panel-app\\src\\lib\\utils.ts": "8", "C:\\Users\\<USER>\\Desktop\\New folder (16)\\electrical-panel-app\\src\\store\\index.ts": "9", "C:\\Users\\<USER>\\Desktop\\New folder (16)\\electrical-panel-app\\src\\types\\index.ts": "10"}, {"size": 10684, "mtime": 1748959921975, "results": "11", "hashOfConfig": "12"}, {"size": 9797, "mtime": 1748959875706, "results": "13", "hashOfConfig": "12"}, {"size": 1279, "mtime": 1748959709232, "results": "14", "hashOfConfig": "12"}, {"size": 6935, "mtime": 1748959770653, "results": "15", "hashOfConfig": "12"}, {"size": 1749, "mtime": 1748959666132, "results": "16", "hashOfConfig": "12"}, {"size": 1876, "mtime": 1748959683811, "results": "17", "hashOfConfig": "12"}, {"size": 5659, "mtime": 1748959605469, "results": "18", "hashOfConfig": "12"}, {"size": 2457, "mtime": 1748959517182, "results": "19", "hashOfConfig": "12"}, {"size": 4968, "mtime": 1748959626701, "results": "20", "hashOfConfig": "12"}, {"size": 3587, "mtime": 1748959558157, "results": "21", "hashOfConfig": "12"}, {"filePath": "22", "messages": "23", "suppressedMessages": "24", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1otw3rd", {"filePath": "25", "messages": "26", "suppressedMessages": "27", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Desktop\\New folder (16)\\electrical-panel-app\\src\\app\\calculations\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (16)\\electrical-panel-app\\src\\app\\dashboard\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (16)\\electrical-panel-app\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (16)\\electrical-panel-app\\src\\app\\page.tsx", ["52", "53", "54", "55"], [], "C:\\Users\\<USER>\\Desktop\\New folder (16)\\electrical-panel-app\\src\\components\\ui\\button.tsx", ["56"], [], "C:\\Users\\<USER>\\Desktop\\New folder (16)\\electrical-panel-app\\src\\components\\ui\\card.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (16)\\electrical-panel-app\\src\\lib\\supabase.ts", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (16)\\electrical-panel-app\\src\\lib\\utils.ts", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (16)\\electrical-panel-app\\src\\store\\index.ts", ["57"], [], "C:\\Users\\<USER>\\Desktop\\New folder (16)\\electrical-panel-app\\src\\types\\index.ts", ["58"], [], {"ruleId": "59", "severity": 2, "message": "60", "line": 3, "column": 16, "nodeType": null, "messageId": "61", "endLine": 3, "endColumn": 27}, {"ruleId": "59", "severity": 2, "message": "62", "line": 9, "column": 3, "nodeType": null, "messageId": "61", "endLine": 9, "endColumn": 8}, {"ruleId": "59", "severity": 2, "message": "63", "line": 10, "column": 3, "nodeType": null, "messageId": "61", "endLine": 10, "endColumn": 14}, {"ruleId": "64", "severity": 2, "message": "65", "line": 130, "column": 55, "nodeType": "66", "messageId": "67", "suggestions": "68"}, {"ruleId": "59", "severity": 2, "message": "69", "line": 41, "column": 32, "nodeType": null, "messageId": "61", "endLine": 41, "endColumn": 39}, {"ruleId": "59", "severity": 2, "message": "70", "line": 49, "column": 13, "nodeType": null, "messageId": "61", "endLine": 49, "endColumn": 16}, {"ruleId": "71", "severity": 2, "message": "72", "line": 70, "column": 34, "nodeType": "73", "messageId": "74", "endLine": 70, "endColumn": 37, "suggestions": "75"}, "@typescript-eslint/no-unused-vars", "'CardContent' is defined but never used.", "unusedVar", "'Users' is defined but never used.", "'CheckCircle' is defined but never used.", "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["76", "77", "78", "79"], "'as<PERSON><PERSON>d' is assigned a value but never used.", "'get' is defined but never used.", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["80", "81"], {"messageId": "82", "data": "83", "fix": "84", "desc": "85"}, {"messageId": "82", "data": "86", "fix": "87", "desc": "88"}, {"messageId": "82", "data": "89", "fix": "90", "desc": "91"}, {"messageId": "82", "data": "92", "fix": "93", "desc": "94"}, {"messageId": "95", "fix": "96", "desc": "97"}, {"messageId": "98", "fix": "99", "desc": "100"}, "replaceWithAlt", {"alt": "101"}, {"range": "102", "text": "103"}, "Replace with `&apos;`.", {"alt": "104"}, {"range": "105", "text": "106"}, "Replace with `&lsquo;`.", {"alt": "107"}, {"range": "108", "text": "109"}, "Replace with `&#39;`.", {"alt": "110"}, {"range": "111", "text": "112"}, "Replace with `&rsquo;`.", "suggestUnknown", {"range": "113", "text": "114"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "115", "text": "116"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", "&apos;", [5088, 5200], "\n                  Searchable database of manufacturers&apos; components with compatibility checking\n                ", "&lsquo;", [5088, 5200], "\n                  Searchable database of manufacturers&lsquo; components with compatibility checking\n                ", "&#39;", [5088, 5200], "\n                  Searchable database of manufacturers&#39; components with compatibility checking\n                ", "&rsquo;", [5088, 5200], "\n                  Searchable database of manufacturers&rsquo; components with compatibility checking\n                ", [1575, 1578], "unknown", [1575, 1578], "never"]