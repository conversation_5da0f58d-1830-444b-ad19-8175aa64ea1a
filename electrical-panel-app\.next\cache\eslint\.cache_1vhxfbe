[{"C:\\Users\\<USER>\\Desktop\\New folder (16)\\electrical-panel-app\\app\\calculations\\page.tsx": "1", "C:\\Users\\<USER>\\Desktop\\New folder (16)\\electrical-panel-app\\app\\dashboard\\page.tsx": "2", "C:\\Users\\<USER>\\Desktop\\New folder (16)\\electrical-panel-app\\app\\layout.tsx": "3", "C:\\Users\\<USER>\\Desktop\\New folder (16)\\electrical-panel-app\\app\\page.tsx": "4", "C:\\Users\\<USER>\\Desktop\\New folder (16)\\electrical-panel-app\\components\\ui\\button.tsx": "5", "C:\\Users\\<USER>\\Desktop\\New folder (16)\\electrical-panel-app\\components\\ui\\card.tsx": "6", "C:\\Users\\<USER>\\Desktop\\New folder (16)\\electrical-panel-app\\lib\\supabase.ts": "7", "C:\\Users\\<USER>\\Desktop\\New folder (16)\\electrical-panel-app\\lib\\utils.ts": "8"}, {"size": 10696, "mtime": 1748960813463, "results": "9", "hashOfConfig": "10"}, {"size": 9805, "mtime": 1748960791837, "results": "11", "hashOfConfig": "10"}, {"size": 1279, "mtime": 1748959709232, "results": "12", "hashOfConfig": "10"}, {"size": 6905, "mtime": 1748962058114, "results": "13", "hashOfConfig": "10"}, {"size": 1736, "mtime": 1748962175113, "results": "14", "hashOfConfig": "10"}, {"size": 1880, "mtime": 1748961806392, "results": "15", "hashOfConfig": "10"}, {"size": 5659, "mtime": 1748959605469, "results": "16", "hashOfConfig": "10"}, {"size": 2457, "mtime": 1748959517182, "results": "17", "hashOfConfig": "10"}, {"filePath": "18", "messages": "19", "suppressedMessages": "20", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1otw3rd", {"filePath": "21", "messages": "22", "suppressedMessages": "23", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\New folder (16)\\electrical-panel-app\\app\\calculations\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (16)\\electrical-panel-app\\app\\dashboard\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (16)\\electrical-panel-app\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (16)\\electrical-panel-app\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (16)\\electrical-panel-app\\components\\ui\\button.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (16)\\electrical-panel-app\\components\\ui\\card.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (16)\\electrical-panel-app\\lib\\supabase.ts", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (16)\\electrical-panel-app\\lib\\utils.ts", [], []]