[{"C:\\Users\\<USER>\\Desktop\\New folder (16)\\electrical-panel-app\\app\\calculations\\page.tsx": "1", "C:\\Users\\<USER>\\Desktop\\New folder (16)\\electrical-panel-app\\app\\dashboard\\page.tsx": "2", "C:\\Users\\<USER>\\Desktop\\New folder (16)\\electrical-panel-app\\app\\layout.tsx": "3", "C:\\Users\\<USER>\\Desktop\\New folder (16)\\electrical-panel-app\\app\\page.tsx": "4", "C:\\Users\\<USER>\\Desktop\\New folder (16)\\electrical-panel-app\\components\\ui\\button.tsx": "5", "C:\\Users\\<USER>\\Desktop\\New folder (16)\\electrical-panel-app\\components\\ui\\card.tsx": "6", "C:\\Users\\<USER>\\Desktop\\New folder (16)\\electrical-panel-app\\lib\\supabase.ts": "7", "C:\\Users\\<USER>\\Desktop\\New folder (16)\\electrical-panel-app\\lib\\utils.ts": "8", "C:\\Users\\<USER>\\Desktop\\New folder (16)\\electrical-panel-app\\pages\\test.js": "9"}, {"size": 10709, "mtime": 1748965955827, "results": "10", "hashOfConfig": "11"}, {"size": 9848, "mtime": 1748965980933, "results": "12", "hashOfConfig": "11"}, {"size": 1289, "mtime": 1748966031512, "results": "13", "hashOfConfig": "11"}, {"size": 6989, "mtime": 1748965969898, "results": "14", "hashOfConfig": "11"}, {"size": 1736, "mtime": 1748962175113, "results": "15", "hashOfConfig": "11"}, {"size": 1880, "mtime": 1748961806392, "results": "16", "hashOfConfig": "11"}, {"size": 5659, "mtime": 1748959605469, "results": "17", "hashOfConfig": "11"}, {"size": 2457, "mtime": 1748959517182, "results": "18", "hashOfConfig": "11"}, {"size": 293, "mtime": 1748962513291, "results": "19", "hashOfConfig": "20"}, {"filePath": "21", "messages": "22", "suppressedMessages": "23", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "t8akdb", {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1yftaip", "C:\\Users\\<USER>\\Desktop\\New folder (16)\\electrical-panel-app\\app\\calculations\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (16)\\electrical-panel-app\\app\\dashboard\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (16)\\electrical-panel-app\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (16)\\electrical-panel-app\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (16)\\electrical-panel-app\\components\\ui\\button.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (16)\\electrical-panel-app\\components\\ui\\card.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (16)\\electrical-panel-app\\lib\\supabase.ts", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (16)\\electrical-panel-app\\lib\\utils.ts", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (16)\\electrical-panel-app\\pages\\test.js", [], []]