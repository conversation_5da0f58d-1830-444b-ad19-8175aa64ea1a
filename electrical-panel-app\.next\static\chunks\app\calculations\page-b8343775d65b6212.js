(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[381],{1631:(e,s,r)=>{Promise.resolve().then(r.bind(r,3588))},3588:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>p});var t=r(5155),a=r(2115),d=r(6874),l=r.n(d),n=r(7168),i=r(8482);let c=(0,r(9946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]);var o=r(1539),m=r(381),x=r(1007),u=r(6740),h=r(3999);function p(){let[e,s]=(0,a.useState)(20),[r,d]=(0,a.useState)(100),[p,f]=(0,a.useState)(240),[g,j]=(0,a.useState)("12"),[b,v]=(0,a.useState)(3),N=(0,h.un)(e,r,g,p),y=(0,h.r4)(e,r,b),C=h.WM.WIRE_AMPACITY[g];return(0,t.jsxs)("div",{className:"min-h-screen bg-background",children:[(0,t.jsx)("header",{className:"border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",children:(0,t.jsxs)("div",{className:"container flex h-16 items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsx)(n.$,{variant:"ghost",size:"icon",asChild:!0,children:(0,t.jsx)(l(),{href:"/dashboard",children:(0,t.jsx)(c,{className:"h-4 w-4"})})}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(o.A,{className:"h-6 w-6 text-primary"}),(0,t.jsx)("span",{className:"text-xl font-bold",children:"ElectriPanel Pro"})]})]}),(0,t.jsxs)("nav",{className:"hidden md:flex items-center space-x-6",children:[(0,t.jsx)(l(),{href:"/dashboard",className:"text-sm font-medium hover:text-primary",children:"Dashboard"}),(0,t.jsx)(l(),{href:"/projects",className:"text-sm font-medium hover:text-primary",children:"Projects"}),(0,t.jsx)(l(),{href:"/design",className:"text-sm font-medium hover:text-primary",children:"Design"}),(0,t.jsx)(l(),{href:"/calculations",className:"text-sm font-medium text-primary",children:"Calculations"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsx)(n.$,{variant:"ghost",size:"icon",children:(0,t.jsx)(m.A,{className:"h-4 w-4"})}),(0,t.jsx)(n.$,{variant:"ghost",size:"icon",children:(0,t.jsx)(x.A,{className:"h-4 w-4"})})]})]})}),(0,t.jsxs)("div",{className:"container py-8",children:[(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsxs)("h1",{className:"text-3xl font-bold mb-2 flex items-center",children:[(0,t.jsx)(u.A,{className:"h-8 w-8 mr-3 text-primary"}),"Electrical Calculations"]}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Calculate voltage drop, wire sizing, and load requirements for your electrical projects."})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,t.jsxs)(i.Zp,{children:[(0,t.jsxs)(i.aR,{children:[(0,t.jsx)(i.ZB,{children:"Circuit Parameters"}),(0,t.jsx)(i.BT,{children:"Enter the circuit parameters to calculate voltage drop and wire sizing"})]}),(0,t.jsxs)(i.Wu,{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium mb-2 block",children:"Current (Amps)"}),(0,t.jsx)("input",{type:"number",value:e,onChange:e=>s(Number(e.target.value)),className:"w-full px-3 py-2 border border-input rounded-md bg-background",min:"0",step:"0.1"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium mb-2 block",children:"Distance (feet)"}),(0,t.jsx)("input",{type:"number",value:r,onChange:e=>d(Number(e.target.value)),className:"w-full px-3 py-2 border border-input rounded-md bg-background",min:"0",step:"1"})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium mb-2 block",children:"Voltage (V)"}),(0,t.jsxs)("select",{value:p,onChange:e=>f(Number(e.target.value)),className:"w-full px-3 py-2 border border-input rounded-md bg-background",children:[(0,t.jsx)("option",{value:120,children:"120V"}),(0,t.jsx)("option",{value:208,children:"208V"}),(0,t.jsx)("option",{value:240,children:"240V"}),(0,t.jsx)("option",{value:480,children:"480V"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium mb-2 block",children:"Wire Size (AWG)"}),(0,t.jsx)("select",{value:g,onChange:e=>j(e.target.value),className:"w-full px-3 py-2 border border-input rounded-md bg-background",children:Object.keys(h.WM.WIRE_AMPACITY).map(e=>(0,t.jsxs)("option",{value:e,children:[e," AWG"]},e))})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium mb-2 block",children:"Max Voltage Drop (%)"}),(0,t.jsx)("input",{type:"number",value:b,onChange:e=>v(Number(e.target.value)),className:"w-full px-3 py-2 border border-input rounded-md bg-background",min:"0",max:"10",step:"0.1"})]})]})]}),(0,t.jsxs)(i.Zp,{children:[(0,t.jsxs)(i.aR,{children:[(0,t.jsx)(i.ZB,{children:"Calculation Results"}),(0,t.jsx)(i.BT,{children:"Based on your input parameters"})]}),(0,t.jsxs)(i.Wu,{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 gap-4",children:[(0,t.jsxs)("div",{className:"p-4 border rounded-lg",children:[(0,t.jsx)("div",{className:"text-sm font-medium text-muted-foreground mb-1",children:"Voltage Drop"}),(0,t.jsxs)("div",{className:"text-2xl font-bold",children:[N,"%"]}),(0,t.jsx)("div",{className:"text-sm text-muted-foreground",children:N<=3?"✓ Within acceptable limits":"⚠ Exceeds 3% recommendation"})]}),(0,t.jsxs)("div",{className:"p-4 border rounded-lg",children:[(0,t.jsx)("div",{className:"text-sm font-medium text-muted-foreground mb-1",children:"Wire Ampacity"}),(0,t.jsxs)("div",{className:"text-2xl font-bold",children:[C," A"]}),(0,t.jsx)("div",{className:"text-sm text-muted-foreground",children:C>=1.25*e?"✓ Adequate for load":"⚠ Undersized for load"})]}),(0,t.jsxs)("div",{className:"p-4 border rounded-lg",children:[(0,t.jsx)("div",{className:"text-sm font-medium text-muted-foreground mb-1",children:"Recommended Wire Size"}),(0,t.jsxs)("div",{className:"text-2xl font-bold",children:[y," AWG"]}),(0,t.jsxs)("div",{className:"text-sm text-muted-foreground",children:["Based on ",b,"% max voltage drop"]})]}),(0,t.jsxs)("div",{className:"p-4 border rounded-lg",children:[(0,t.jsx)("div",{className:"text-sm font-medium text-muted-foreground mb-1",children:"Load Factor"}),(0,t.jsxs)("div",{className:"text-2xl font-bold",children:[C?Math.round(e/C*100):0,"%"]}),(0,t.jsx)("div",{className:"text-sm text-muted-foreground",children:"Wire utilization"})]})]}),(0,t.jsxs)("div",{className:"pt-4 border-t",children:[(0,t.jsx)("h4",{className:"font-medium mb-2",children:"NEC References"}),(0,t.jsxs)("ul",{className:"text-sm text-muted-foreground space-y-1",children:[(0,t.jsx)("li",{children:"• Article 210.19(A) - Branch Circuit Conductors"}),(0,t.jsx)("li",{children:"• Article 215.2 - Feeder Conductors"}),(0,t.jsx)("li",{children:"• Table 310.15(B)(16) - Ampacity Ratings"})]})]})]})]})]}),(0,t.jsxs)("div",{className:"mt-12",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold mb-6",children:"Additional Tools"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,t.jsx)(i.Zp,{className:"cursor-pointer hover:shadow-md transition-shadow",children:(0,t.jsxs)(i.aR,{children:[(0,t.jsx)(i.ZB,{className:"text-lg",children:"Load Calculation"}),(0,t.jsx)(i.BT,{children:"Calculate total connected and demand loads per NEC Article 220"})]})}),(0,t.jsx)(i.Zp,{className:"cursor-pointer hover:shadow-md transition-shadow",children:(0,t.jsxs)(i.aR,{children:[(0,t.jsx)(i.ZB,{className:"text-lg",children:"Short Circuit Analysis"}),(0,t.jsx)(i.BT,{children:"Determine fault current levels and protective device coordination"})]})}),(0,t.jsx)(i.Zp,{className:"cursor-pointer hover:shadow-md transition-shadow",children:(0,t.jsxs)(i.aR,{children:[(0,t.jsx)(i.ZB,{className:"text-lg",children:"Conduit Fill"}),(0,t.jsx)(i.BT,{children:"Calculate conduit fill percentages and sizing requirements"})]})})]})]})]})]})}},3999:(e,s,r)=>{"use strict";r.d(s,{WM:()=>l,cn:()=>d,r4:()=>i,un:()=>n});var t=r(2596),a=r(9688);function d(){for(var e=arguments.length,s=Array(e),r=0;r<e;r++)s[r]=arguments[r];return(0,a.QP)((0,t.$)(s))}let l={VOLTAGE_120:120,VOLTAGE_240:240,VOLTAGE_208:208,VOLTAGE_480:480,WIRE_RESISTANCE:{14:3.07,12:1.93,10:1.21,8:.764,6:.491,4:.308,3:.245,2:.194,1:.154,"1/0":.122,"2/0":.0967,"3/0":.0766,"4/0":.0608},WIRE_AMPACITY:{14:20,12:25,10:35,8:50,6:65,4:85,3:100,2:115,1:130,"1/0":150,"2/0":175,"3/0":200,"4/0":230}};function n(e,s,r){let t=arguments.length>3&&void 0!==arguments[3]?arguments[3]:240,a=l.WIRE_RESISTANCE[r];return a?Math.round(2*e*a*s/1e3/t*1e4)/100:0}function i(e,s){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:3;for(let t of Object.keys(l.WIRE_AMPACITY))if(l.WIRE_AMPACITY[t]>=1.25*e&&n(e,s,t)<=r)return t;return"4/0"}},7168:(e,s,r)=>{"use strict";r.d(s,{$:()=>i});var t=r(5155),a=r(2115),d=r(2085),l=r(3999);let n=(0,d.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),i=a.forwardRef((e,s)=>{let{className:r,variant:a,size:d,...i}=e;return(0,t.jsx)("button",{className:(0,l.cn)(n({variant:a,size:d,className:r})),ref:s,...i})});i.displayName="Button"},8482:(e,s,r)=>{"use strict";r.d(s,{BT:()=>c,Wu:()=>o,ZB:()=>i,Zp:()=>l,aR:()=>n});var t=r(5155),a=r(2115),d=r(3999);let l=a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)("div",{ref:s,className:(0,d.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...a})});l.displayName="Card";let n=a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)("div",{ref:s,className:(0,d.cn)("flex flex-col space-y-1.5 p-6",r),...a})});n.displayName="CardHeader";let i=a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)("h3",{ref:s,className:(0,d.cn)("text-2xl font-semibold leading-none tracking-tight",r),...a})});i.displayName="CardTitle";let c=a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)("p",{ref:s,className:(0,d.cn)("text-sm text-muted-foreground",r),...a})});c.displayName="CardDescription";let o=a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)("div",{ref:s,className:(0,d.cn)("p-6 pt-0",r),...a})});o.displayName="CardContent",a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)("div",{ref:s,className:(0,d.cn)("flex items-center p-6 pt-0",r),...a})}).displayName="CardFooter"}},e=>{var s=s=>e(e.s=s);e.O(0,[874,830,441,684,358],()=>s(1631)),_N_E=e.O()}]);