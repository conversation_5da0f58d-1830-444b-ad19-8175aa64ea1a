(()=>{var e={};e.id=492,e.ids=[492],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1263:(e,r,n)=>{"use strict";n.r(r),n.d(r,{GlobalError:()=>o.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>u,tree:()=>d});var t=n(5239),s=n(8088),i=n(8170),o=n.n(i),a=n(893),l={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);n.d(r,l);let d={children:["",{children:["/_not-found",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(n.t.bind(n,7398,23)),"next/dist/client/components/not-found-error"]}]},{}]},{layout:[()=>Promise.resolve().then(n.bind(n,8014)),"C:\\Users\\<USER>\\Desktop\\New folder (16)\\electrical-panel-app\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(n.t.bind(n,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(n.t.bind(n,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(n.t.bind(n,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=[],p={require:n,loadChunk:()=>Promise.resolve()},u=new t.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/_not-found/page",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},2704:()=>{},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3501:(e,r,n)=>{Promise.resolve().then(n.t.bind(n,6444,23)),Promise.resolve().then(n.t.bind(n,6042,23)),Promise.resolve().then(n.t.bind(n,8170,23)),Promise.resolve().then(n.t.bind(n,9477,23)),Promise.resolve().then(n.t.bind(n,9345,23)),Promise.resolve().then(n.t.bind(n,2089,23)),Promise.resolve().then(n.t.bind(n,6577,23)),Promise.resolve().then(n.t.bind(n,1307,23))},3873:e=>{"use strict";e.exports=require("path")},4941:(e,r,n)=>{Promise.resolve().then(n.t.bind(n,6346,23)),Promise.resolve().then(n.t.bind(n,7924,23)),Promise.resolve().then(n.t.bind(n,5656,23)),Promise.resolve().then(n.t.bind(n,99,23)),Promise.resolve().then(n.t.bind(n,8243,23)),Promise.resolve().then(n.t.bind(n,8827,23)),Promise.resolve().then(n.t.bind(n,2763,23)),Promise.resolve().then(n.t.bind(n,7173,23))},5902:()=>{},8014:(e,r,n)=>{"use strict";n.r(r),n.d(r,{default:()=>d,metadata:()=>l});var t=n(7413),s=n(260),i=n.n(s),o=n(3298),a=n.n(o);n(2704);let l={title:"ElectriPanel Pro - Electrical Panel Design & Management",description:"Professional electrical panel board design, planning, and documentation for electricians, engineers, and contractors.",keywords:["electrical panel","panel board","electrical design","NEC compliance","electrical calculations"],authors:[{name:"ElectriPanel Pro"}],viewport:"width=device-width, initial-scale=1",themeColor:"#3b82f6",manifest:"/manifest.json"};function d({children:e}){return(0,t.jsx)("html",{lang:"en",suppressHydrationWarning:!0,children:(0,t.jsx)("body",{className:`${i().variable} ${a().variable} antialiased min-h-screen bg-background font-sans`,children:(0,t.jsx)("div",{className:"relative flex min-h-screen flex-col",children:(0,t.jsx)("main",{className:"flex-1",children:e})})})})}},8950:()=>{},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var r=require("../../webpack-runtime.js");r.C(e);var n=e=>r(r.s=e),t=r.X(0,[447,423],()=>n(1263));module.exports=t})();