(()=>{var e={};e.id=381,e.ids=[381],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},858:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>t});let t=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\New folder (16)\\\\electrical-panel-app\\\\app\\\\calculations\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\New folder (16)\\electrical-panel-app\\app\\calculations\\page.tsx","default")},2704:()=>{},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3501:(e,r,s)=>{Promise.resolve().then(s.t.bind(s,6444,23)),Promise.resolve().then(s.t.bind(s,6042,23)),Promise.resolve().then(s.t.bind(s,8170,23)),Promise.resolve().then(s.t.bind(s,9477,23)),Promise.resolve().then(s.t.bind(s,9345,23)),Promise.resolve().then(s.t.bind(s,2089,23)),Promise.resolve().then(s.t.bind(s,6577,23)),Promise.resolve().then(s.t.bind(s,1307,23))},3873:e=>{"use strict";e.exports=require("path")},4934:(e,r,s)=>{"use strict";s.d(r,{$:()=>d});var t=s(687),a=s(3210),n=s(4224),i=s(6241);let l=(0,n.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=a.forwardRef(({className:e,variant:r,size:s,...a},n)=>(0,t.jsx)("button",{className:(0,i.cn)(l({variant:r,size:s,className:e})),ref:n,...a}));d.displayName="Button"},4941:(e,r,s)=>{Promise.resolve().then(s.t.bind(s,6346,23)),Promise.resolve().then(s.t.bind(s,7924,23)),Promise.resolve().then(s.t.bind(s,5656,23)),Promise.resolve().then(s.t.bind(s,99,23)),Promise.resolve().then(s.t.bind(s,8243,23)),Promise.resolve().then(s.t.bind(s,8827,23)),Promise.resolve().then(s.t.bind(s,2763,23)),Promise.resolve().then(s.t.bind(s,7173,23))},5094:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>o});var t=s(5239),a=s(8088),n=s(8170),i=s.n(n),l=s(893),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);s.d(r,d);let o={children:["",{children:["calculations",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,858)),"C:\\Users\\<USER>\\Desktop\\New folder (16)\\electrical-panel-app\\app\\calculations\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,6055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,8014)),"C:\\Users\\<USER>\\Desktop\\New folder (16)\\electrical-panel-app\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,6055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\New folder (16)\\electrical-panel-app\\app\\calculations\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},u=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/calculations/page",pathname:"/calculations",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},5192:(e,r,s)=>{"use strict";s.d(r,{BT:()=>o,Wu:()=>c,ZB:()=>d,Zp:()=>i,aR:()=>l});var t=s(687),a=s(3210),n=s(6241);let i=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("div",{ref:s,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...r}));i.displayName="Card";let l=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("div",{ref:s,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",e),...r}));l.displayName="CardHeader";let d=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("h3",{ref:s,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",e),...r}));d.displayName="CardTitle";let o=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("p",{ref:s,className:(0,n.cn)("text-sm text-muted-foreground",e),...r}));o.displayName="CardDescription";let c=a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("div",{ref:s,className:(0,n.cn)("p-6 pt-0",e),...r}));c.displayName="CardContent",a.forwardRef(({className:e,...r},s)=>(0,t.jsx)("div",{ref:s,className:(0,n.cn)("flex items-center p-6 pt-0",e),...r})).displayName="CardFooter"},5902:()=>{},6055:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>a});var t=s(1658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,t.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},6241:(e,r,s)=>{"use strict";s.d(r,{WM:()=>i,cn:()=>n,r4:()=>d,un:()=>l});var t=s(9384),a=s(2348);function n(...e){return(0,a.QP)((0,t.$)(e))}let i={VOLTAGE_120:120,VOLTAGE_240:240,VOLTAGE_208:208,VOLTAGE_480:480,WIRE_RESISTANCE:{14:3.07,12:1.93,10:1.21,8:.764,6:.491,4:.308,3:.245,2:.194,1:.154,"1/0":.122,"2/0":.0967,"3/0":.0766,"4/0":.0608},WIRE_AMPACITY:{14:20,12:25,10:35,8:50,6:65,4:85,3:100,2:115,1:130,"1/0":150,"2/0":175,"3/0":200,"4/0":230}};function l(e,r,s,t=240){let a=i.WIRE_RESISTANCE[s];return a?Math.round(2*e*a*r/1e3/t*1e4)/100:0}function d(e,r,s=3){for(let t of Object.keys(i.WIRE_AMPACITY))if(i.WIRE_AMPACITY[t]>=1.25*e&&l(e,r,t)<=s)return t;return"4/0"}},6689:(e,r,s)=>{Promise.resolve().then(s.bind(s,858))},6961:(e,r,s)=>{Promise.resolve().then(s.bind(s,7502))},7502:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>h});var t=s(687),a=s(3210),n=s(5814),i=s.n(n),l=s(4934),d=s(5192);let o=(0,s(2688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]);var c=s(5583),m=s(4027),u=s(8869),x=s(9402),p=s(6241);function h(){let[e,r]=(0,a.useState)(20),[s,n]=(0,a.useState)(100),[h,f]=(0,a.useState)(240),[b,v]=(0,a.useState)("12"),[g,j]=(0,a.useState)(3),N=(0,p.un)(e,s,b,h),y=(0,p.r4)(e,s,g),w=p.WM.WIRE_AMPACITY[b];return(0,t.jsxs)("div",{className:"min-h-screen bg-background",children:[(0,t.jsx)("header",{className:"border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",children:(0,t.jsxs)("div",{className:"container flex h-16 items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsx)(l.$,{variant:"ghost",size:"icon",asChild:!0,children:(0,t.jsx)(i(),{href:"/dashboard",children:(0,t.jsx)(o,{className:"h-4 w-4"})})}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(c.A,{className:"h-6 w-6 text-primary"}),(0,t.jsx)("span",{className:"text-xl font-bold",children:"ElectriPanel Pro"})]})]}),(0,t.jsxs)("nav",{className:"hidden md:flex items-center space-x-6",children:[(0,t.jsx)(i(),{href:"/dashboard",className:"text-sm font-medium hover:text-primary",children:"Dashboard"}),(0,t.jsx)(i(),{href:"/projects",className:"text-sm font-medium hover:text-primary",children:"Projects"}),(0,t.jsx)(i(),{href:"/design",className:"text-sm font-medium hover:text-primary",children:"Design"}),(0,t.jsx)(i(),{href:"/calculations",className:"text-sm font-medium text-primary",children:"Calculations"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsx)(l.$,{variant:"ghost",size:"icon",children:(0,t.jsx)(m.A,{className:"h-4 w-4"})}),(0,t.jsx)(l.$,{variant:"ghost",size:"icon",children:(0,t.jsx)(u.A,{className:"h-4 w-4"})})]})]})}),(0,t.jsxs)("div",{className:"container py-8",children:[(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsxs)("h1",{className:"text-3xl font-bold mb-2 flex items-center",children:[(0,t.jsx)(x.A,{className:"h-8 w-8 mr-3 text-primary"}),"Electrical Calculations"]}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Calculate voltage drop, wire sizing, and load requirements for your electrical projects."})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,t.jsxs)(d.Zp,{children:[(0,t.jsxs)(d.aR,{children:[(0,t.jsx)(d.ZB,{children:"Circuit Parameters"}),(0,t.jsx)(d.BT,{children:"Enter the circuit parameters to calculate voltage drop and wire sizing"})]}),(0,t.jsxs)(d.Wu,{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium mb-2 block",children:"Current (Amps)"}),(0,t.jsx)("input",{type:"number",value:e,onChange:e=>r(Number(e.target.value)),className:"w-full px-3 py-2 border border-input rounded-md bg-background",min:"0",step:"0.1"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium mb-2 block",children:"Distance (feet)"}),(0,t.jsx)("input",{type:"number",value:s,onChange:e=>n(Number(e.target.value)),className:"w-full px-3 py-2 border border-input rounded-md bg-background",min:"0",step:"1"})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium mb-2 block",children:"Voltage (V)"}),(0,t.jsxs)("select",{value:h,onChange:e=>f(Number(e.target.value)),className:"w-full px-3 py-2 border border-input rounded-md bg-background",children:[(0,t.jsx)("option",{value:120,children:"120V"}),(0,t.jsx)("option",{value:208,children:"208V"}),(0,t.jsx)("option",{value:240,children:"240V"}),(0,t.jsx)("option",{value:480,children:"480V"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium mb-2 block",children:"Wire Size (AWG)"}),(0,t.jsx)("select",{value:b,onChange:e=>v(e.target.value),className:"w-full px-3 py-2 border border-input rounded-md bg-background",children:Object.keys(p.WM.WIRE_AMPACITY).map(e=>(0,t.jsxs)("option",{value:e,children:[e," AWG"]},e))})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium mb-2 block",children:"Max Voltage Drop (%)"}),(0,t.jsx)("input",{type:"number",value:g,onChange:e=>j(Number(e.target.value)),className:"w-full px-3 py-2 border border-input rounded-md bg-background",min:"0",max:"10",step:"0.1"})]})]})]}),(0,t.jsxs)(d.Zp,{children:[(0,t.jsxs)(d.aR,{children:[(0,t.jsx)(d.ZB,{children:"Calculation Results"}),(0,t.jsx)(d.BT,{children:"Based on your input parameters"})]}),(0,t.jsxs)(d.Wu,{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 gap-4",children:[(0,t.jsxs)("div",{className:"p-4 border rounded-lg",children:[(0,t.jsx)("div",{className:"text-sm font-medium text-muted-foreground mb-1",children:"Voltage Drop"}),(0,t.jsxs)("div",{className:"text-2xl font-bold",children:[N,"%"]}),(0,t.jsx)("div",{className:"text-sm text-muted-foreground",children:N<=3?"✓ Within acceptable limits":"⚠ Exceeds 3% recommendation"})]}),(0,t.jsxs)("div",{className:"p-4 border rounded-lg",children:[(0,t.jsx)("div",{className:"text-sm font-medium text-muted-foreground mb-1",children:"Wire Ampacity"}),(0,t.jsxs)("div",{className:"text-2xl font-bold",children:[w," A"]}),(0,t.jsx)("div",{className:"text-sm text-muted-foreground",children:w>=1.25*e?"✓ Adequate for load":"⚠ Undersized for load"})]}),(0,t.jsxs)("div",{className:"p-4 border rounded-lg",children:[(0,t.jsx)("div",{className:"text-sm font-medium text-muted-foreground mb-1",children:"Recommended Wire Size"}),(0,t.jsxs)("div",{className:"text-2xl font-bold",children:[y," AWG"]}),(0,t.jsxs)("div",{className:"text-sm text-muted-foreground",children:["Based on ",g,"% max voltage drop"]})]}),(0,t.jsxs)("div",{className:"p-4 border rounded-lg",children:[(0,t.jsx)("div",{className:"text-sm font-medium text-muted-foreground mb-1",children:"Load Factor"}),(0,t.jsxs)("div",{className:"text-2xl font-bold",children:[w?Math.round(e/w*100):0,"%"]}),(0,t.jsx)("div",{className:"text-sm text-muted-foreground",children:"Wire utilization"})]})]}),(0,t.jsxs)("div",{className:"pt-4 border-t",children:[(0,t.jsx)("h4",{className:"font-medium mb-2",children:"NEC References"}),(0,t.jsxs)("ul",{className:"text-sm text-muted-foreground space-y-1",children:[(0,t.jsx)("li",{children:"• Article 210.19(A) - Branch Circuit Conductors"}),(0,t.jsx)("li",{children:"• Article 215.2 - Feeder Conductors"}),(0,t.jsx)("li",{children:"• Table 310.15(B)(16) - Ampacity Ratings"})]})]})]})]})]}),(0,t.jsxs)("div",{className:"mt-12",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold mb-6",children:"Additional Tools"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,t.jsx)(d.Zp,{className:"cursor-pointer hover:shadow-md transition-shadow",children:(0,t.jsxs)(d.aR,{children:[(0,t.jsx)(d.ZB,{className:"text-lg",children:"Load Calculation"}),(0,t.jsx)(d.BT,{children:"Calculate total connected and demand loads per NEC Article 220"})]})}),(0,t.jsx)(d.Zp,{className:"cursor-pointer hover:shadow-md transition-shadow",children:(0,t.jsxs)(d.aR,{children:[(0,t.jsx)(d.ZB,{className:"text-lg",children:"Short Circuit Analysis"}),(0,t.jsx)(d.BT,{children:"Determine fault current levels and protective device coordination"})]})}),(0,t.jsx)(d.Zp,{className:"cursor-pointer hover:shadow-md transition-shadow",children:(0,t.jsxs)(d.aR,{children:[(0,t.jsx)(d.ZB,{className:"text-lg",children:"Conduit Fill"}),(0,t.jsx)(d.BT,{children:"Calculate conduit fill percentages and sizing requirements"})]})})]})]})]})]})}},8014:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>o,metadata:()=>d});var t=s(7413),a=s(260),n=s.n(a),i=s(3298),l=s.n(i);s(2704);let d={title:"ElectriPanel Pro - Electrical Panel Design & Management",description:"Professional electrical panel board design, planning, and documentation for electricians, engineers, and contractors.",keywords:["electrical panel","panel board","electrical design","NEC compliance","electrical calculations"],authors:[{name:"ElectriPanel Pro"}],viewport:"width=device-width, initial-scale=1",themeColor:"#3b82f6",manifest:"/manifest.json"};function o({children:e}){return(0,t.jsx)("html",{lang:"en",suppressHydrationWarning:!0,children:(0,t.jsx)("body",{className:`${n().variable} ${l().variable} antialiased min-h-screen bg-background font-sans`,children:(0,t.jsx)("div",{className:"relative flex min-h-screen flex-col",children:(0,t.jsx)("main",{className:"flex-1",children:e})})})})}},8950:()=>{},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")}};var r=require("../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[447,423,567,830],()=>s(5094));module.exports=t})();